package com.celeste.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.io.Serializable;
import java.lang.Boolean;
import java.lang.CloneNotSupportedException;
import java.lang.Cloneable;
import java.lang.IllegalArgumentException;
import java.lang.IllegalStateException;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.lang.System;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import org.babyfish.jimmer.CircularReferenceException;
import org.babyfish.jimmer.DraftConsumer;
import org.babyfish.jimmer.ImmutableObjects;
import org.babyfish.jimmer.UnloadedException;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.jackson.ImmutableModuleRequiredException;
import org.babyfish.jimmer.lang.OldChain;
import org.babyfish.jimmer.meta.ImmutablePropCategory;
import org.babyfish.jimmer.meta.ImmutableType;
import org.babyfish.jimmer.meta.PropId;
import org.babyfish.jimmer.runtime.DraftContext;
import org.babyfish.jimmer.runtime.DraftSpi;
import org.babyfish.jimmer.runtime.ImmutableSpi;
import org.babyfish.jimmer.runtime.Internal;
import org.babyfish.jimmer.runtime.NonSharedList;
import org.babyfish.jimmer.runtime.Visibility;
import org.babyfish.jimmer.sql.ManyToMany;
import org.babyfish.jimmer.sql.OneToMany;
import org.babyfish.jimmer.sql.collection.IdViewList;
import org.babyfish.jimmer.sql.collection.MutableIdViewList;
import org.jetbrains.annotations.Nullable;

@GeneratedBy(
        type = User.class
)
public interface UserDraft extends User, BaseDraft {
    UserDraft.Producer $ = Producer.INSTANCE;

    @OldChain
    UserDraft setId(long id);

    @OldChain
    UserDraft setCreateTime(Date createTime);

    @OldChain
    UserDraft setModifyTime(Date modifyTime);

    @OldChain
    UserDraft setUsername(String username);

    @OldChain
    UserDraft setPassword(String password);

    @OldChain
    UserDraft setEmail(String email);

    @OldChain
    UserDraft setDescription(String description);

    @OldChain
    UserDraft setGender(String gender);

    @OldChain
    UserDraft setHome(String home);

    @OldChain
    UserDraft setStatus(String status);

    List<TypeDraft> types(boolean autoCreate);

    @OldChain
    UserDraft setTypes(List<Type> types);

    @OldChain
    UserDraft addIntoTypes(DraftConsumer<TypeDraft> block);

    @OldChain
    UserDraft addIntoTypes(Type base, DraftConsumer<TypeDraft> block);

    List<UserDraft> adores(boolean autoCreate);

    @OldChain
    UserDraft setAdores(List<User> adores);

    @OldChain
    UserDraft addIntoAdores(DraftConsumer<UserDraft> block);

    @OldChain
    UserDraft addIntoAdores(User base, DraftConsumer<UserDraft> block);

    List<UserDraft> followers(boolean autoCreate);

    @OldChain
    UserDraft setFollowers(List<User> followers);

    @OldChain
    UserDraft addIntoFollowers(DraftConsumer<UserDraft> block);

    @OldChain
    UserDraft addIntoFollowers(User base, DraftConsumer<UserDraft> block);

    List<RoleDraft> roles(boolean autoCreate);

    @OldChain
    UserDraft setRoles(List<Role> roles);

    @OldChain
    UserDraft addIntoRoles(DraftConsumer<RoleDraft> block);

    @OldChain
    UserDraft addIntoRoles(Role base, DraftConsumer<RoleDraft> block);

    List<VideoDraft> videos(boolean autoCreate);

    @OldChain
    UserDraft setVideos(List<Video> videos);

    @OldChain
    UserDraft addIntoVideos(DraftConsumer<VideoDraft> block);

    @OldChain
    UserDraft addIntoVideos(Video base, DraftConsumer<VideoDraft> block);

    List<CollectionDraft> collections(boolean autoCreate);

    @OldChain
    UserDraft setCollections(List<Collection> collections);

    @OldChain
    UserDraft addIntoCollections(DraftConsumer<CollectionDraft> block);

    @OldChain
    UserDraft addIntoCollections(Collection base, DraftConsumer<CollectionDraft> block);

    List<Long> typeIds(boolean autoCreate);

    @OldChain
    UserDraft setTypeIds(List<Long> typeIds);

    @GeneratedBy(
            type = User.class
    )
    class Producer {
        static final Producer INSTANCE = new Producer();

        public static final int SLOT_ID = 3;

        public static final int SLOT_CREATE_TIME = 0;

        public static final int SLOT_MODIFY_TIME = 1;

        public static final int SLOT_BETWEEN_TIME = 2;

        public static final int SLOT_USERNAME = 4;

        public static final int SLOT_PASSWORD = 5;

        public static final int SLOT_EMAIL = 6;

        public static final int SLOT_DESCRIPTION = 7;

        public static final int SLOT_GENDER = 8;

        public static final int SLOT_HOME = 9;

        public static final int SLOT_STATUS = 10;

        public static final int SLOT_TYPES = 11;

        public static final int SLOT_ADORES = 12;

        public static final int SLOT_FOLLOWERS = 13;

        public static final int SLOT_ROLES = 14;

        public static final int SLOT_VIDEOS = 15;

        public static final int SLOT_COLLECTIONS = 16;

        public static final int SLOT_TYPE_IDS = 17;

        public static final int SLOT_COLLECTED_VIDEOS = 18;

        public static final int SLOT_ADORES_SUM = 19;

        public static final int SLOT_FOLLOWERS_SUM = 20;

        public static final int SLOT_ROLE_LIST = 21;

        public static final int SLOT_AUTHORITY_LIST = 22;

        public static final int SLOT_USER_IS_ADORE = 23;

        public static final ImmutableType TYPE = ImmutableType
            .newBuilder(
                "0.8.150",
                User.class,
                Collections.singleton(BaseDraft.Producer.TYPE),
                (ctx, base) -> new DraftImpl(ctx, (User)base)
            )
            .redefine("createTime", SLOT_CREATE_TIME)
            .redefine("modifyTime", SLOT_MODIFY_TIME)
            .redefine("betweenTime", SLOT_BETWEEN_TIME)
            .id(SLOT_ID, "id", long.class)
            .add(SLOT_USERNAME, "username", ImmutablePropCategory.SCALAR, String.class, false)
            .add(SLOT_PASSWORD, "password", ImmutablePropCategory.SCALAR, String.class, false)
            .add(SLOT_EMAIL, "email", ImmutablePropCategory.SCALAR, String.class, false)
            .add(SLOT_DESCRIPTION, "description", ImmutablePropCategory.SCALAR, String.class, true)
            .add(SLOT_GENDER, "gender", ImmutablePropCategory.SCALAR, String.class, false)
            .add(SLOT_HOME, "home", ImmutablePropCategory.SCALAR, String.class, false)
            .add(SLOT_STATUS, "status", ImmutablePropCategory.SCALAR, String.class, false)
            .add(SLOT_TYPES, "types", ManyToMany.class, Type.class, false)
            .add(SLOT_ADORES, "adores", ManyToMany.class, User.class, false)
            .add(SLOT_FOLLOWERS, "followers", ManyToMany.class, User.class, false)
            .add(SLOT_ROLES, "roles", ManyToMany.class, Role.class, false)
            .add(SLOT_VIDEOS, "videos", OneToMany.class, Video.class, false)
            .add(SLOT_COLLECTIONS, "collections", OneToMany.class, Collection.class, false)
            .add(SLOT_TYPE_IDS, "typeIds", ImmutablePropCategory.SCALAR_LIST, Long.class, false)
            .add(SLOT_COLLECTED_VIDEOS, "collectedVideos", ImmutablePropCategory.SCALAR_LIST, Long.class, false)
            .add(SLOT_ADORES_SUM, "adoresSum", ImmutablePropCategory.SCALAR, long.class, false)
            .add(SLOT_FOLLOWERS_SUM, "followersSum", ImmutablePropCategory.SCALAR, long.class, false)
            .add(SLOT_ROLE_LIST, "roleList", ImmutablePropCategory.SCALAR_LIST, String.class, false)
            .add(SLOT_AUTHORITY_LIST, "authorityList", ImmutablePropCategory.SCALAR_LIST, String.class, false)
            .add(SLOT_USER_IS_ADORE, "userIsAdore", ImmutablePropCategory.SCALAR, boolean.class, false)
            .build();

        private Producer() {
        }

        public User produce(DraftConsumer<UserDraft> block) {
            return produce(null, block);
        }

        public User produce(User base, DraftConsumer<UserDraft> block) {
            return (User)Internal.produce(TYPE, base, block);
        }

        @GeneratedBy(
                type = User.class
        )
        @JsonPropertyOrder({"dummyPropForJacksonError__", "createTime", "modifyTime", "betweenTime", "id", "username", "password", "email", "description", "gender", "home", "status", "types", "adores", "followers", "roles", "videos", "collections", "typeIds", "collectedVideos", "adoresSum", "followersSum", "roleList", "authorityList", "userIsAdore"})
        public abstract interface Implementor extends User, ImmutableSpi {
            @Override
            default Object __get(PropId prop) {
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		return __get(prop.asName());
                    case SLOT_CREATE_TIME:
                    		return createTime();
                    case SLOT_MODIFY_TIME:
                    		return modifyTime();
                    case SLOT_BETWEEN_TIME:
                    		return betweenTime();
                    case SLOT_ID:
                    		return (Long)id();
                    case SLOT_USERNAME:
                    		return username();
                    case SLOT_PASSWORD:
                    		return password();
                    case SLOT_EMAIL:
                    		return email();
                    case SLOT_DESCRIPTION:
                    		return description();
                    case SLOT_GENDER:
                    		return gender();
                    case SLOT_HOME:
                    		return home();
                    case SLOT_STATUS:
                    		return status();
                    case SLOT_TYPES:
                    		return types();
                    case SLOT_ADORES:
                    		return adores();
                    case SLOT_FOLLOWERS:
                    		return followers();
                    case SLOT_ROLES:
                    		return roles();
                    case SLOT_VIDEOS:
                    		return videos();
                    case SLOT_COLLECTIONS:
                    		return collections();
                    case SLOT_TYPE_IDS:
                    		return typeIds();
                    case SLOT_COLLECTED_VIDEOS:
                    		return collectedVideos();
                    case SLOT_ADORES_SUM:
                    		return (Long)adoresSum();
                    case SLOT_FOLLOWERS_SUM:
                    		return (Long)followersSum();
                    case SLOT_ROLE_LIST:
                    		return roleList();
                    case SLOT_AUTHORITY_LIST:
                    		return authorityList();
                    case SLOT_USER_IS_ADORE:
                    		return (Boolean)userIsAdore();
                    default: throw new IllegalArgumentException("Illegal property name for \"com.celeste.entity.User\": \"" + prop + "\"");
                }
            }

            @Override
            default Object __get(String prop) {
                switch (prop) {
                    case "createTime":
                    		return createTime();
                    case "modifyTime":
                    		return modifyTime();
                    case "betweenTime":
                    		return betweenTime();
                    case "id":
                    		return (Long)id();
                    case "username":
                    		return username();
                    case "password":
                    		return password();
                    case "email":
                    		return email();
                    case "description":
                    		return description();
                    case "gender":
                    		return gender();
                    case "home":
                    		return home();
                    case "status":
                    		return status();
                    case "types":
                    		return types();
                    case "adores":
                    		return adores();
                    case "followers":
                    		return followers();
                    case "roles":
                    		return roles();
                    case "videos":
                    		return videos();
                    case "collections":
                    		return collections();
                    case "typeIds":
                    		return typeIds();
                    case "collectedVideos":
                    		return collectedVideos();
                    case "adoresSum":
                    		return (Long)adoresSum();
                    case "followersSum":
                    		return (Long)followersSum();
                    case "roleList":
                    		return roleList();
                    case "authorityList":
                    		return authorityList();
                    case "userIsAdore":
                    		return (Boolean)userIsAdore();
                    default: throw new IllegalArgumentException("Illegal property name for \"com.celeste.entity.User\": \"" + prop + "\"");
                }
            }

            default long getId() {
                return id();
            }

            default Date getCreateTime() {
                return createTime();
            }

            @Nullable
            default Date getModifyTime() {
                return modifyTime();
            }

            default String getBetweenTime() {
                return betweenTime();
            }

            default String getUsername() {
                return username();
            }

            default String getPassword() {
                return password();
            }

            default String getEmail() {
                return email();
            }

            @Nullable
            default String getDescription() {
                return description();
            }

            default String getGender() {
                return gender();
            }

            default String getHome() {
                return home();
            }

            default String getStatus() {
                return status();
            }

            default List<Type> getTypes() {
                return types();
            }

            default List<User> getAdores() {
                return adores();
            }

            default List<User> getFollowers() {
                return followers();
            }

            default List<Role> getRoles() {
                return roles();
            }

            default List<Video> getVideos() {
                return videos();
            }

            default List<Collection> getCollections() {
                return collections();
            }

            default List<Long> getTypeIds() {
                return typeIds();
            }

            default List<Long> getCollectedVideos() {
                return collectedVideos();
            }

            default long getAdoresSum() {
                return adoresSum();
            }

            default long getFollowersSum() {
                return followersSum();
            }

            default List<String> getRoleList() {
                return roleList();
            }

            default List<String> getAuthorityList() {
                return authorityList();
            }

            default boolean isUserIsAdore() {
                return userIsAdore();
            }

            @Override
            default ImmutableType __type() {
                return TYPE;
            }

            default int getDummyPropForJacksonError__() {
                throw new ImmutableModuleRequiredException();
            }
        }

        @GeneratedBy(
                type = User.class
        )
        private static class Impl implements Implementor, Cloneable, Serializable {
            private Visibility __visibility;

            long __idValue;

            boolean __idLoaded = false;

            Date __createTimeValue;

            Date __modifyTimeValue;

            boolean __modifyTimeLoaded = false;

            String __usernameValue;

            String __passwordValue;

            String __emailValue;

            String __descriptionValue;

            boolean __descriptionLoaded = false;

            String __genderValue;

            String __homeValue;

            String __statusValue;

            NonSharedList<Type> __typesValue;

            NonSharedList<User> __adoresValue;

            NonSharedList<User> __followersValue;

            NonSharedList<Role> __rolesValue;

            NonSharedList<Video> __videosValue;

            NonSharedList<Collection> __collectionsValue;

            Impl() {
                __visibility = Visibility.of(24);
                __visibility.show(SLOT_BETWEEN_TIME, false);
                __visibility.show(SLOT_TYPE_IDS, false);
                __visibility.show(SLOT_COLLECTED_VIDEOS, false);
                __visibility.show(SLOT_ADORES_SUM, false);
                __visibility.show(SLOT_FOLLOWERS_SUM, false);
                __visibility.show(SLOT_ROLE_LIST, false);
                __visibility.show(SLOT_AUTHORITY_LIST, false);
                __visibility.show(SLOT_USER_IS_ADORE, false);
            }

            @Override
            @JsonIgnore
            public long id() {
                if (!__idLoaded) {
                    throw new UnloadedException(User.class, "id");
                }
                return __idValue;
            }

            @Override
            @JsonIgnore
            public Date createTime() {
                if (__createTimeValue == null) {
                    throw new UnloadedException(User.class, "createTime");
                }
                return __createTimeValue;
            }

            @Override
            @JsonIgnore
            @Nullable
            public Date modifyTime() {
                if (!__modifyTimeLoaded) {
                    throw new UnloadedException(User.class, "modifyTime");
                }
                return __modifyTimeValue;
            }

            @Override
            @JsonIgnore
            public String username() {
                if (__usernameValue == null) {
                    throw new UnloadedException(User.class, "username");
                }
                return __usernameValue;
            }

            @Override
            @JsonIgnore
            public String password() {
                if (__passwordValue == null) {
                    throw new UnloadedException(User.class, "password");
                }
                return __passwordValue;
            }

            @Override
            @JsonIgnore
            public String email() {
                if (__emailValue == null) {
                    throw new UnloadedException(User.class, "email");
                }
                return __emailValue;
            }

            @Override
            @JsonIgnore
            @Nullable
            public String description() {
                if (!__descriptionLoaded) {
                    throw new UnloadedException(User.class, "description");
                }
                return __descriptionValue;
            }

            @Override
            @JsonIgnore
            public String gender() {
                if (__genderValue == null) {
                    throw new UnloadedException(User.class, "gender");
                }
                return __genderValue;
            }

            @Override
            @JsonIgnore
            public String home() {
                if (__homeValue == null) {
                    throw new UnloadedException(User.class, "home");
                }
                return __homeValue;
            }

            @Override
            @JsonIgnore
            public String status() {
                if (__statusValue == null) {
                    throw new UnloadedException(User.class, "status");
                }
                return __statusValue;
            }

            @Override
            @JsonIgnore
            public List<Type> types() {
                if (__typesValue == null) {
                    throw new UnloadedException(User.class, "types");
                }
                return __typesValue;
            }

            @Override
            @JsonIgnore
            public List<User> adores() {
                if (__adoresValue == null) {
                    throw new UnloadedException(User.class, "adores");
                }
                return __adoresValue;
            }

            @Override
            @JsonIgnore
            public List<User> followers() {
                if (__followersValue == null) {
                    throw new UnloadedException(User.class, "followers");
                }
                return __followersValue;
            }

            @Override
            @JsonIgnore
            public List<Role> roles() {
                if (__rolesValue == null) {
                    throw new UnloadedException(User.class, "roles");
                }
                return __rolesValue;
            }

            @Override
            @JsonIgnore
            public List<Video> videos() {
                if (__videosValue == null) {
                    throw new UnloadedException(User.class, "videos");
                }
                return __videosValue;
            }

            @Override
            @JsonIgnore
            public List<Collection> collections() {
                if (__collectionsValue == null) {
                    throw new UnloadedException(User.class, "collections");
                }
                return __collectionsValue;
            }

            @Override
            @JsonIgnore
            public List<Long> typeIds() {
                return new IdViewList<>(TypeDraft.Producer.TYPE, types());
            }

            @Override
            public Impl clone() {
                try {
                    return (Impl)super.clone();
                } catch(CloneNotSupportedException ex) {
                    throw new AssertionError(ex);
                }
            }

            @Override
            public boolean __isLoaded(PropId prop) {
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		return __isLoaded(prop.asName());
                    case SLOT_CREATE_TIME:
                    		return __createTimeValue != null;
                    case SLOT_MODIFY_TIME:
                    		return __modifyTimeLoaded;
                    case SLOT_BETWEEN_TIME:
                    		return __isLoaded(PropId.byIndex(SLOT_CREATE_TIME));
                    case SLOT_ID:
                    		return __idLoaded;
                    case SLOT_USERNAME:
                    		return __usernameValue != null;
                    case SLOT_PASSWORD:
                    		return __passwordValue != null;
                    case SLOT_EMAIL:
                    		return __emailValue != null;
                    case SLOT_DESCRIPTION:
                    		return __descriptionLoaded;
                    case SLOT_GENDER:
                    		return __genderValue != null;
                    case SLOT_HOME:
                    		return __homeValue != null;
                    case SLOT_STATUS:
                    		return __statusValue != null;
                    case SLOT_TYPES:
                    		return __typesValue != null;
                    case SLOT_ADORES:
                    		return __adoresValue != null;
                    case SLOT_FOLLOWERS:
                    		return __followersValue != null;
                    case SLOT_ROLES:
                    		return __rolesValue != null;
                    case SLOT_VIDEOS:
                    		return __videosValue != null;
                    case SLOT_COLLECTIONS:
                    		return __collectionsValue != null;
                    case SLOT_TYPE_IDS:
                    		return __isLoaded(PropId.byIndex(SLOT_TYPES)) && types().stream().allMatch(__each -> 
                                ((ImmutableSpi)__each).__isLoaded(PropId.byIndex(TypeDraft.Producer.SLOT_ID))
                            );
                    case SLOT_COLLECTED_VIDEOS:
                    		return ImmutableObjects.isLoadedChain(this, PropId.byIndex(Producer.SLOT_COLLECTIONS), PropId.byIndex(CollectionDraft.Producer.SLOT_VIDEO_IDS));
                    case SLOT_ADORES_SUM:
                    		return __isLoaded(PropId.byIndex(SLOT_ADORES));
                    case SLOT_FOLLOWERS_SUM:
                    		return __isLoaded(PropId.byIndex(SLOT_FOLLOWERS));
                    case SLOT_ROLE_LIST:
                    		return ImmutableObjects.isLoadedChain(this, PropId.byIndex(Producer.SLOT_ROLES), PropId.byIndex(RoleDraft.Producer.SLOT_NAME));
                    case SLOT_AUTHORITY_LIST:
                    		return ImmutableObjects.isLoadedChain(this, PropId.byIndex(Producer.SLOT_ROLES), PropId.byIndex(RoleDraft.Producer.SLOT_AUTHORITIES), PropId.byIndex(AuthorityDraft.Producer.SLOT_NAME));
                    case SLOT_USER_IS_ADORE:
                    		return ImmutableObjects.isLoadedChain(this, PropId.byIndex(Producer.SLOT_FOLLOWERS), PropId.byIndex(Producer.SLOT_ID));
                    default: throw new IllegalArgumentException("Illegal property name for \"com.celeste.entity.User\": \"" + prop + "\"");
                }
            }

            @Override
            public boolean __isLoaded(String prop) {
                switch (prop) {
                    case "createTime":
                    		return __createTimeValue != null;
                    case "modifyTime":
                    		return __modifyTimeLoaded;
                    case "betweenTime":
                    		return __isLoaded(PropId.byIndex(SLOT_CREATE_TIME));
                    case "id":
                    		return __idLoaded;
                    case "username":
                    		return __usernameValue != null;
                    case "password":
                    		return __passwordValue != null;
                    case "email":
                    		return __emailValue != null;
                    case "description":
                    		return __descriptionLoaded;
                    case "gender":
                    		return __genderValue != null;
                    case "home":
                    		return __homeValue != null;
                    case "status":
                    		return __statusValue != null;
                    case "types":
                    		return __typesValue != null;
                    case "adores":
                    		return __adoresValue != null;
                    case "followers":
                    		return __followersValue != null;
                    case "roles":
                    		return __rolesValue != null;
                    case "videos":
                    		return __videosValue != null;
                    case "collections":
                    		return __collectionsValue != null;
                    case "typeIds":
                    		return __isLoaded(PropId.byIndex(SLOT_TYPES)) && types().stream().allMatch(__each -> 
                                ((ImmutableSpi)__each).__isLoaded(PropId.byIndex(TypeDraft.Producer.SLOT_ID))
                            );
                    case "collectedVideos":
                    		return ImmutableObjects.isLoadedChain(this, PropId.byIndex(Producer.SLOT_COLLECTIONS), PropId.byIndex(CollectionDraft.Producer.SLOT_VIDEO_IDS));
                    case "adoresSum":
                    		return __isLoaded(PropId.byIndex(SLOT_ADORES));
                    case "followersSum":
                    		return __isLoaded(PropId.byIndex(SLOT_FOLLOWERS));
                    case "roleList":
                    		return ImmutableObjects.isLoadedChain(this, PropId.byIndex(Producer.SLOT_ROLES), PropId.byIndex(RoleDraft.Producer.SLOT_NAME));
                    case "authorityList":
                    		return ImmutableObjects.isLoadedChain(this, PropId.byIndex(Producer.SLOT_ROLES), PropId.byIndex(RoleDraft.Producer.SLOT_AUTHORITIES), PropId.byIndex(AuthorityDraft.Producer.SLOT_NAME));
                    case "userIsAdore":
                    		return ImmutableObjects.isLoadedChain(this, PropId.byIndex(Producer.SLOT_FOLLOWERS), PropId.byIndex(Producer.SLOT_ID));
                    default: throw new IllegalArgumentException("Illegal property name for \"com.celeste.entity.User\": \"" + prop + "\"");
                }
            }

            @Override
            public boolean __isVisible(PropId prop) {
                if (__visibility == null) {
                    return true;
                }
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		return __isVisible(prop.asName());
                    case SLOT_CREATE_TIME:
                    		return __visibility.visible(SLOT_CREATE_TIME);
                    case SLOT_MODIFY_TIME:
                    		return __visibility.visible(SLOT_MODIFY_TIME);
                    case SLOT_BETWEEN_TIME:
                    		return __visibility.visible(SLOT_BETWEEN_TIME);
                    case SLOT_ID:
                    		return __visibility.visible(SLOT_ID);
                    case SLOT_USERNAME:
                    		return __visibility.visible(SLOT_USERNAME);
                    case SLOT_PASSWORD:
                    		return __visibility.visible(SLOT_PASSWORD);
                    case SLOT_EMAIL:
                    		return __visibility.visible(SLOT_EMAIL);
                    case SLOT_DESCRIPTION:
                    		return __visibility.visible(SLOT_DESCRIPTION);
                    case SLOT_GENDER:
                    		return __visibility.visible(SLOT_GENDER);
                    case SLOT_HOME:
                    		return __visibility.visible(SLOT_HOME);
                    case SLOT_STATUS:
                    		return __visibility.visible(SLOT_STATUS);
                    case SLOT_TYPES:
                    		return __visibility.visible(SLOT_TYPES);
                    case SLOT_ADORES:
                    		return __visibility.visible(SLOT_ADORES);
                    case SLOT_FOLLOWERS:
                    		return __visibility.visible(SLOT_FOLLOWERS);
                    case SLOT_ROLES:
                    		return __visibility.visible(SLOT_ROLES);
                    case SLOT_VIDEOS:
                    		return __visibility.visible(SLOT_VIDEOS);
                    case SLOT_COLLECTIONS:
                    		return __visibility.visible(SLOT_COLLECTIONS);
                    case SLOT_TYPE_IDS:
                    		return __visibility.visible(SLOT_TYPE_IDS);
                    case SLOT_COLLECTED_VIDEOS:
                    		return __visibility.visible(SLOT_COLLECTED_VIDEOS);
                    case SLOT_ADORES_SUM:
                    		return __visibility.visible(SLOT_ADORES_SUM);
                    case SLOT_FOLLOWERS_SUM:
                    		return __visibility.visible(SLOT_FOLLOWERS_SUM);
                    case SLOT_ROLE_LIST:
                    		return __visibility.visible(SLOT_ROLE_LIST);
                    case SLOT_AUTHORITY_LIST:
                    		return __visibility.visible(SLOT_AUTHORITY_LIST);
                    case SLOT_USER_IS_ADORE:
                    		return __visibility.visible(SLOT_USER_IS_ADORE);
                    default: return true;
                }
            }

            @Override
            public boolean __isVisible(String prop) {
                if (__visibility == null) {
                    return true;
                }
                switch (prop) {
                    case "createTime":
                    		return __visibility.visible(SLOT_CREATE_TIME);
                    case "modifyTime":
                    		return __visibility.visible(SLOT_MODIFY_TIME);
                    case "betweenTime":
                    		return __visibility.visible(SLOT_BETWEEN_TIME);
                    case "id":
                    		return __visibility.visible(SLOT_ID);
                    case "username":
                    		return __visibility.visible(SLOT_USERNAME);
                    case "password":
                    		return __visibility.visible(SLOT_PASSWORD);
                    case "email":
                    		return __visibility.visible(SLOT_EMAIL);
                    case "description":
                    		return __visibility.visible(SLOT_DESCRIPTION);
                    case "gender":
                    		return __visibility.visible(SLOT_GENDER);
                    case "home":
                    		return __visibility.visible(SLOT_HOME);
                    case "status":
                    		return __visibility.visible(SLOT_STATUS);
                    case "types":
                    		return __visibility.visible(SLOT_TYPES);
                    case "adores":
                    		return __visibility.visible(SLOT_ADORES);
                    case "followers":
                    		return __visibility.visible(SLOT_FOLLOWERS);
                    case "roles":
                    		return __visibility.visible(SLOT_ROLES);
                    case "videos":
                    		return __visibility.visible(SLOT_VIDEOS);
                    case "collections":
                    		return __visibility.visible(SLOT_COLLECTIONS);
                    case "typeIds":
                    		return __visibility.visible(SLOT_TYPE_IDS);
                    case "collectedVideos":
                    		return __visibility.visible(SLOT_COLLECTED_VIDEOS);
                    case "adoresSum":
                    		return __visibility.visible(SLOT_ADORES_SUM);
                    case "followersSum":
                    		return __visibility.visible(SLOT_FOLLOWERS_SUM);
                    case "roleList":
                    		return __visibility.visible(SLOT_ROLE_LIST);
                    case "authorityList":
                    		return __visibility.visible(SLOT_AUTHORITY_LIST);
                    case "userIsAdore":
                    		return __visibility.visible(SLOT_USER_IS_ADORE);
                    default: return true;
                }
            }

            @Override
            public int hashCode() {
                int hash = __visibility != null ? __visibility.hashCode() : 0;
                if (__idLoaded) {
                    hash = 31 * hash + Long.hashCode(__idValue);
                    // If entity-id is loaded, return directly
                    return hash;
                }
                if (__createTimeValue != null) {
                    hash = 31 * hash + __createTimeValue.hashCode();
                }
                if (__modifyTimeLoaded && __modifyTimeValue != null) {
                    hash = 31 * hash + __modifyTimeValue.hashCode();
                }
                if (__usernameValue != null) {
                    hash = 31 * hash + __usernameValue.hashCode();
                }
                if (__passwordValue != null) {
                    hash = 31 * hash + __passwordValue.hashCode();
                }
                if (__emailValue != null) {
                    hash = 31 * hash + __emailValue.hashCode();
                }
                if (__descriptionLoaded && __descriptionValue != null) {
                    hash = 31 * hash + __descriptionValue.hashCode();
                }
                if (__genderValue != null) {
                    hash = 31 * hash + __genderValue.hashCode();
                }
                if (__homeValue != null) {
                    hash = 31 * hash + __homeValue.hashCode();
                }
                if (__statusValue != null) {
                    hash = 31 * hash + __statusValue.hashCode();
                }
                if (__typesValue != null) {
                    hash = 31 * hash + __typesValue.hashCode();
                }
                if (__adoresValue != null) {
                    hash = 31 * hash + __adoresValue.hashCode();
                }
                if (__followersValue != null) {
                    hash = 31 * hash + __followersValue.hashCode();
                }
                if (__rolesValue != null) {
                    hash = 31 * hash + __rolesValue.hashCode();
                }
                if (__videosValue != null) {
                    hash = 31 * hash + __videosValue.hashCode();
                }
                if (__collectionsValue != null) {
                    hash = 31 * hash + __collectionsValue.hashCode();
                }
                return hash;
            }

            private int __shallowHashCode() {
                int hash = __visibility != null ? __visibility.hashCode() : 0;
                if (__idLoaded) {
                    hash = 31 * hash + Long.hashCode(__idValue);
                }
                if (__createTimeValue != null) {
                    hash = 31 * hash + System.identityHashCode(__createTimeValue);
                }
                if (__modifyTimeLoaded) {
                    hash = 31 * hash + System.identityHashCode(__modifyTimeValue);
                }
                if (__usernameValue != null) {
                    hash = 31 * hash + System.identityHashCode(__usernameValue);
                }
                if (__passwordValue != null) {
                    hash = 31 * hash + System.identityHashCode(__passwordValue);
                }
                if (__emailValue != null) {
                    hash = 31 * hash + System.identityHashCode(__emailValue);
                }
                if (__descriptionLoaded) {
                    hash = 31 * hash + System.identityHashCode(__descriptionValue);
                }
                if (__genderValue != null) {
                    hash = 31 * hash + System.identityHashCode(__genderValue);
                }
                if (__homeValue != null) {
                    hash = 31 * hash + System.identityHashCode(__homeValue);
                }
                if (__statusValue != null) {
                    hash = 31 * hash + System.identityHashCode(__statusValue);
                }
                if (__typesValue != null) {
                    hash = 31 * hash + System.identityHashCode(__typesValue);
                }
                if (__adoresValue != null) {
                    hash = 31 * hash + System.identityHashCode(__adoresValue);
                }
                if (__followersValue != null) {
                    hash = 31 * hash + System.identityHashCode(__followersValue);
                }
                if (__rolesValue != null) {
                    hash = 31 * hash + System.identityHashCode(__rolesValue);
                }
                if (__videosValue != null) {
                    hash = 31 * hash + System.identityHashCode(__videosValue);
                }
                if (__collectionsValue != null) {
                    hash = 31 * hash + System.identityHashCode(__collectionsValue);
                }
                return hash;
            }

            @Override
            public int __hashCode(boolean shallow) {
                return shallow ? __shallowHashCode() : hashCode();
            }

            @Override
            public boolean equals(Object obj) {
                if (obj == null || !(obj instanceof Implementor)) {
                    return false;
                }
                Implementor __other = (Implementor)obj;
                if (__isVisible(PropId.byIndex(SLOT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ID))) {
                    return false;
                }
                boolean __idLoaded = this.__idLoaded;
                if (__idLoaded != __other.__isLoaded(PropId.byIndex(SLOT_ID))) {
                    return false;
                }
                if (__idLoaded) {
                    // If entity-id is loaded, return directly
                    return __idValue == __other.id();
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATE_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_CREATE_TIME))) {
                    return false;
                }
                boolean __createTimeLoaded = __createTimeValue != null;
                if (__createTimeLoaded != __other.__isLoaded(PropId.byIndex(SLOT_CREATE_TIME))) {
                    return false;
                }
                if (__createTimeLoaded && !Objects.equals(__createTimeValue, __other.createTime())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_MODIFY_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_MODIFY_TIME))) {
                    return false;
                }
                boolean __modifyTimeLoaded = this.__modifyTimeLoaded;
                if (__modifyTimeLoaded != __other.__isLoaded(PropId.byIndex(SLOT_MODIFY_TIME))) {
                    return false;
                }
                if (__modifyTimeLoaded && !Objects.equals(__modifyTimeValue, __other.modifyTime())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_BETWEEN_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_BETWEEN_TIME))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_USERNAME)) != __other.__isVisible(PropId.byIndex(SLOT_USERNAME))) {
                    return false;
                }
                boolean __usernameLoaded = __usernameValue != null;
                if (__usernameLoaded != __other.__isLoaded(PropId.byIndex(SLOT_USERNAME))) {
                    return false;
                }
                if (__usernameLoaded && !Objects.equals(__usernameValue, __other.username())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_PASSWORD)) != __other.__isVisible(PropId.byIndex(SLOT_PASSWORD))) {
                    return false;
                }
                boolean __passwordLoaded = __passwordValue != null;
                if (__passwordLoaded != __other.__isLoaded(PropId.byIndex(SLOT_PASSWORD))) {
                    return false;
                }
                if (__passwordLoaded && !Objects.equals(__passwordValue, __other.password())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_EMAIL)) != __other.__isVisible(PropId.byIndex(SLOT_EMAIL))) {
                    return false;
                }
                boolean __emailLoaded = __emailValue != null;
                if (__emailLoaded != __other.__isLoaded(PropId.byIndex(SLOT_EMAIL))) {
                    return false;
                }
                if (__emailLoaded && !Objects.equals(__emailValue, __other.email())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_DESCRIPTION)) != __other.__isVisible(PropId.byIndex(SLOT_DESCRIPTION))) {
                    return false;
                }
                boolean __descriptionLoaded = this.__descriptionLoaded;
                if (__descriptionLoaded != __other.__isLoaded(PropId.byIndex(SLOT_DESCRIPTION))) {
                    return false;
                }
                if (__descriptionLoaded && !Objects.equals(__descriptionValue, __other.description())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_GENDER)) != __other.__isVisible(PropId.byIndex(SLOT_GENDER))) {
                    return false;
                }
                boolean __genderLoaded = __genderValue != null;
                if (__genderLoaded != __other.__isLoaded(PropId.byIndex(SLOT_GENDER))) {
                    return false;
                }
                if (__genderLoaded && !Objects.equals(__genderValue, __other.gender())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_HOME)) != __other.__isVisible(PropId.byIndex(SLOT_HOME))) {
                    return false;
                }
                boolean __homeLoaded = __homeValue != null;
                if (__homeLoaded != __other.__isLoaded(PropId.byIndex(SLOT_HOME))) {
                    return false;
                }
                if (__homeLoaded && !Objects.equals(__homeValue, __other.home())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_STATUS)) != __other.__isVisible(PropId.byIndex(SLOT_STATUS))) {
                    return false;
                }
                boolean __statusLoaded = __statusValue != null;
                if (__statusLoaded != __other.__isLoaded(PropId.byIndex(SLOT_STATUS))) {
                    return false;
                }
                if (__statusLoaded && !Objects.equals(__statusValue, __other.status())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_TYPES)) != __other.__isVisible(PropId.byIndex(SLOT_TYPES))) {
                    return false;
                }
                boolean __typesLoaded = __typesValue != null;
                if (__typesLoaded != __other.__isLoaded(PropId.byIndex(SLOT_TYPES))) {
                    return false;
                }
                if (__typesLoaded && !Objects.equals(__typesValue, __other.types())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_ADORES)) != __other.__isVisible(PropId.byIndex(SLOT_ADORES))) {
                    return false;
                }
                boolean __adoresLoaded = __adoresValue != null;
                if (__adoresLoaded != __other.__isLoaded(PropId.byIndex(SLOT_ADORES))) {
                    return false;
                }
                if (__adoresLoaded && !Objects.equals(__adoresValue, __other.adores())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_FOLLOWERS)) != __other.__isVisible(PropId.byIndex(SLOT_FOLLOWERS))) {
                    return false;
                }
                boolean __followersLoaded = __followersValue != null;
                if (__followersLoaded != __other.__isLoaded(PropId.byIndex(SLOT_FOLLOWERS))) {
                    return false;
                }
                if (__followersLoaded && !Objects.equals(__followersValue, __other.followers())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_ROLES)) != __other.__isVisible(PropId.byIndex(SLOT_ROLES))) {
                    return false;
                }
                boolean __rolesLoaded = __rolesValue != null;
                if (__rolesLoaded != __other.__isLoaded(PropId.byIndex(SLOT_ROLES))) {
                    return false;
                }
                if (__rolesLoaded && !Objects.equals(__rolesValue, __other.roles())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_VIDEOS)) != __other.__isVisible(PropId.byIndex(SLOT_VIDEOS))) {
                    return false;
                }
                boolean __videosLoaded = __videosValue != null;
                if (__videosLoaded != __other.__isLoaded(PropId.byIndex(SLOT_VIDEOS))) {
                    return false;
                }
                if (__videosLoaded && !Objects.equals(__videosValue, __other.videos())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_COLLECTIONS)) != __other.__isVisible(PropId.byIndex(SLOT_COLLECTIONS))) {
                    return false;
                }
                boolean __collectionsLoaded = __collectionsValue != null;
                if (__collectionsLoaded != __other.__isLoaded(PropId.byIndex(SLOT_COLLECTIONS))) {
                    return false;
                }
                if (__collectionsLoaded && !Objects.equals(__collectionsValue, __other.collections())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_TYPE_IDS)) != __other.__isVisible(PropId.byIndex(SLOT_TYPE_IDS))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_COLLECTED_VIDEOS)) != __other.__isVisible(PropId.byIndex(SLOT_COLLECTED_VIDEOS))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_ADORES_SUM)) != __other.__isVisible(PropId.byIndex(SLOT_ADORES_SUM))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_FOLLOWERS_SUM)) != __other.__isVisible(PropId.byIndex(SLOT_FOLLOWERS_SUM))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_ROLE_LIST)) != __other.__isVisible(PropId.byIndex(SLOT_ROLE_LIST))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_AUTHORITY_LIST)) != __other.__isVisible(PropId.byIndex(SLOT_AUTHORITY_LIST))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_USER_IS_ADORE)) != __other.__isVisible(PropId.byIndex(SLOT_USER_IS_ADORE))) {
                    return false;
                }
                return true;
            }

            private boolean __shallowEquals(Object obj) {
                if (obj == null || !(obj instanceof Implementor)) {
                    return false;
                }
                Implementor __other = (Implementor)obj;
                if (__isVisible(PropId.byIndex(SLOT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ID))) {
                    return false;
                }
                boolean __idLoaded = this.__idLoaded;
                if (__idLoaded != __other.__isLoaded(PropId.byIndex(SLOT_ID))) {
                    return false;
                }
                if (__idLoaded && __idValue != __other.id()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATE_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_CREATE_TIME))) {
                    return false;
                }
                boolean __createTimeLoaded = __createTimeValue != null;
                if (__createTimeLoaded != __other.__isLoaded(PropId.byIndex(SLOT_CREATE_TIME))) {
                    return false;
                }
                if (__createTimeLoaded && __createTimeValue != __other.createTime()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_MODIFY_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_MODIFY_TIME))) {
                    return false;
                }
                boolean __modifyTimeLoaded = this.__modifyTimeLoaded;
                if (__modifyTimeLoaded != __other.__isLoaded(PropId.byIndex(SLOT_MODIFY_TIME))) {
                    return false;
                }
                if (__modifyTimeLoaded && __modifyTimeValue != __other.modifyTime()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_BETWEEN_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_BETWEEN_TIME))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_USERNAME)) != __other.__isVisible(PropId.byIndex(SLOT_USERNAME))) {
                    return false;
                }
                boolean __usernameLoaded = __usernameValue != null;
                if (__usernameLoaded != __other.__isLoaded(PropId.byIndex(SLOT_USERNAME))) {
                    return false;
                }
                if (__usernameLoaded && __usernameValue != __other.username()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_PASSWORD)) != __other.__isVisible(PropId.byIndex(SLOT_PASSWORD))) {
                    return false;
                }
                boolean __passwordLoaded = __passwordValue != null;
                if (__passwordLoaded != __other.__isLoaded(PropId.byIndex(SLOT_PASSWORD))) {
                    return false;
                }
                if (__passwordLoaded && __passwordValue != __other.password()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_EMAIL)) != __other.__isVisible(PropId.byIndex(SLOT_EMAIL))) {
                    return false;
                }
                boolean __emailLoaded = __emailValue != null;
                if (__emailLoaded != __other.__isLoaded(PropId.byIndex(SLOT_EMAIL))) {
                    return false;
                }
                if (__emailLoaded && __emailValue != __other.email()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_DESCRIPTION)) != __other.__isVisible(PropId.byIndex(SLOT_DESCRIPTION))) {
                    return false;
                }
                boolean __descriptionLoaded = this.__descriptionLoaded;
                if (__descriptionLoaded != __other.__isLoaded(PropId.byIndex(SLOT_DESCRIPTION))) {
                    return false;
                }
                if (__descriptionLoaded && __descriptionValue != __other.description()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_GENDER)) != __other.__isVisible(PropId.byIndex(SLOT_GENDER))) {
                    return false;
                }
                boolean __genderLoaded = __genderValue != null;
                if (__genderLoaded != __other.__isLoaded(PropId.byIndex(SLOT_GENDER))) {
                    return false;
                }
                if (__genderLoaded && __genderValue != __other.gender()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_HOME)) != __other.__isVisible(PropId.byIndex(SLOT_HOME))) {
                    return false;
                }
                boolean __homeLoaded = __homeValue != null;
                if (__homeLoaded != __other.__isLoaded(PropId.byIndex(SLOT_HOME))) {
                    return false;
                }
                if (__homeLoaded && __homeValue != __other.home()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_STATUS)) != __other.__isVisible(PropId.byIndex(SLOT_STATUS))) {
                    return false;
                }
                boolean __statusLoaded = __statusValue != null;
                if (__statusLoaded != __other.__isLoaded(PropId.byIndex(SLOT_STATUS))) {
                    return false;
                }
                if (__statusLoaded && __statusValue != __other.status()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_TYPES)) != __other.__isVisible(PropId.byIndex(SLOT_TYPES))) {
                    return false;
                }
                boolean __typesLoaded = __typesValue != null;
                if (__typesLoaded != __other.__isLoaded(PropId.byIndex(SLOT_TYPES))) {
                    return false;
                }
                if (__typesLoaded && __typesValue != __other.types()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_ADORES)) != __other.__isVisible(PropId.byIndex(SLOT_ADORES))) {
                    return false;
                }
                boolean __adoresLoaded = __adoresValue != null;
                if (__adoresLoaded != __other.__isLoaded(PropId.byIndex(SLOT_ADORES))) {
                    return false;
                }
                if (__adoresLoaded && __adoresValue != __other.adores()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_FOLLOWERS)) != __other.__isVisible(PropId.byIndex(SLOT_FOLLOWERS))) {
                    return false;
                }
                boolean __followersLoaded = __followersValue != null;
                if (__followersLoaded != __other.__isLoaded(PropId.byIndex(SLOT_FOLLOWERS))) {
                    return false;
                }
                if (__followersLoaded && __followersValue != __other.followers()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_ROLES)) != __other.__isVisible(PropId.byIndex(SLOT_ROLES))) {
                    return false;
                }
                boolean __rolesLoaded = __rolesValue != null;
                if (__rolesLoaded != __other.__isLoaded(PropId.byIndex(SLOT_ROLES))) {
                    return false;
                }
                if (__rolesLoaded && __rolesValue != __other.roles()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_VIDEOS)) != __other.__isVisible(PropId.byIndex(SLOT_VIDEOS))) {
                    return false;
                }
                boolean __videosLoaded = __videosValue != null;
                if (__videosLoaded != __other.__isLoaded(PropId.byIndex(SLOT_VIDEOS))) {
                    return false;
                }
                if (__videosLoaded && __videosValue != __other.videos()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_COLLECTIONS)) != __other.__isVisible(PropId.byIndex(SLOT_COLLECTIONS))) {
                    return false;
                }
                boolean __collectionsLoaded = __collectionsValue != null;
                if (__collectionsLoaded != __other.__isLoaded(PropId.byIndex(SLOT_COLLECTIONS))) {
                    return false;
                }
                if (__collectionsLoaded && __collectionsValue != __other.collections()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_TYPE_IDS)) != __other.__isVisible(PropId.byIndex(SLOT_TYPE_IDS))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_COLLECTED_VIDEOS)) != __other.__isVisible(PropId.byIndex(SLOT_COLLECTED_VIDEOS))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_ADORES_SUM)) != __other.__isVisible(PropId.byIndex(SLOT_ADORES_SUM))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_FOLLOWERS_SUM)) != __other.__isVisible(PropId.byIndex(SLOT_FOLLOWERS_SUM))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_ROLE_LIST)) != __other.__isVisible(PropId.byIndex(SLOT_ROLE_LIST))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_AUTHORITY_LIST)) != __other.__isVisible(PropId.byIndex(SLOT_AUTHORITY_LIST))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_USER_IS_ADORE)) != __other.__isVisible(PropId.byIndex(SLOT_USER_IS_ADORE))) {
                    return false;
                }
                return true;
            }

            @Override
            public boolean __equals(Object obj, boolean shallow) {
                return shallow ? __shallowEquals(obj) : equals(obj);
            }

            @Override
            public String toString() {
                return ImmutableObjects.toString(this);
            }
        }

        @GeneratedBy(
                type = User.class
        )
        private static class DraftImpl implements Implementor, DraftSpi, UserDraft {
            private DraftContext __ctx;

            private Impl __base;

            private Impl __modified;

            private boolean __resolving;

            private User __resolved;

            DraftImpl(DraftContext ctx, User base) {
                __ctx = ctx;
                if (base != null) {
                    __base = (Impl)base;
                }
                else {
                    __modified = new Impl();
                }
            }

            @Override
            public boolean __isLoaded(PropId prop) {
                return (__modified!= null ? __modified : __base).__isLoaded(prop);
            }

            @Override
            public boolean __isLoaded(String prop) {
                return (__modified!= null ? __modified : __base).__isLoaded(prop);
            }

            @Override
            public boolean __isVisible(PropId prop) {
                return (__modified!= null ? __modified : __base).__isVisible(prop);
            }

            @Override
            public boolean __isVisible(String prop) {
                return (__modified!= null ? __modified : __base).__isVisible(prop);
            }

            @Override
            public int hashCode() {
                return (__modified!= null ? __modified : __base).hashCode();
            }

            @Override
            public int __hashCode(boolean shallow) {
                return (__modified!= null ? __modified : __base).__hashCode(shallow);
            }

            @Override
            public boolean equals(Object obj) {
                return (__modified!= null ? __modified : __base).equals(obj);
            }

            @Override
            public boolean __equals(Object obj, boolean shallow) {
                return (__modified!= null ? __modified : __base).__equals(obj, shallow);
            }

            @Override
            public String toString() {
                return ImmutableObjects.toString(this);
            }

            @Override
            @JsonIgnore
            public long id() {
                return (__modified!= null ? __modified : __base).id();
            }

            @Override
            public UserDraft setId(long id) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                Impl __tmpModified = __modified();
                __tmpModified.__idValue = id;
                __tmpModified.__idLoaded = true;
                return this;
            }

            @Override
            @JsonIgnore
            public Date createTime() {
                return (__modified!= null ? __modified : __base).createTime();
            }

            @Override
            public UserDraft setCreateTime(Date createTime) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (createTime == null) {
                    throw new IllegalArgumentException(
                        "'createTime' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__createTimeValue = createTime;
                return this;
            }

            @Override
            @JsonIgnore
            @Nullable
            public Date modifyTime() {
                return (__modified!= null ? __modified : __base).modifyTime();
            }

            @Override
            public UserDraft setModifyTime(Date modifyTime) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                Impl __tmpModified = __modified();
                __tmpModified.__modifyTimeValue = modifyTime;
                __tmpModified.__modifyTimeLoaded = true;
                return this;
            }

            @Override
            @JsonIgnore
            public String betweenTime() {
                return (__modified!= null ? __modified : __base).betweenTime();
            }

            @Override
            @JsonIgnore
            public String username() {
                return (__modified!= null ? __modified : __base).username();
            }

            @Override
            public UserDraft setUsername(String username) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (username == null) {
                    throw new IllegalArgumentException(
                        "'username' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__usernameValue = username;
                return this;
            }

            @Override
            @JsonIgnore
            public String password() {
                return (__modified!= null ? __modified : __base).password();
            }

            @Override
            public UserDraft setPassword(String password) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (password == null) {
                    throw new IllegalArgumentException(
                        "'password' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__passwordValue = password;
                return this;
            }

            @Override
            @JsonIgnore
            public String email() {
                return (__modified!= null ? __modified : __base).email();
            }

            @Override
            public UserDraft setEmail(String email) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (email == null) {
                    throw new IllegalArgumentException(
                        "'email' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__emailValue = email;
                return this;
            }

            @Override
            @JsonIgnore
            @Nullable
            public String description() {
                return (__modified!= null ? __modified : __base).description();
            }

            @Override
            public UserDraft setDescription(String description) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                Impl __tmpModified = __modified();
                __tmpModified.__descriptionValue = description;
                __tmpModified.__descriptionLoaded = true;
                return this;
            }

            @Override
            @JsonIgnore
            public String gender() {
                return (__modified!= null ? __modified : __base).gender();
            }

            @Override
            public UserDraft setGender(String gender) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (gender == null) {
                    throw new IllegalArgumentException(
                        "'gender' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__genderValue = gender;
                return this;
            }

            @Override
            @JsonIgnore
            public String home() {
                return (__modified!= null ? __modified : __base).home();
            }

            @Override
            public UserDraft setHome(String home) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (home == null) {
                    throw new IllegalArgumentException(
                        "'home' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__homeValue = home;
                return this;
            }

            @Override
            @JsonIgnore
            public String status() {
                return (__modified!= null ? __modified : __base).status();
            }

            @Override
            public UserDraft setStatus(String status) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (status == null) {
                    throw new IllegalArgumentException(
                        "'status' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__statusValue = status;
                return this;
            }

            @Override
            @JsonIgnore
            public List<Type> types() {
                return __ctx.toDraftList((__modified!= null ? __modified : __base).types(), Type.class, true);
            }

            @Override
            public List<TypeDraft> types(boolean autoCreate) {
                if (autoCreate && (!__isLoaded(PropId.byIndex(SLOT_TYPES)))) {
                    setTypes(new ArrayList<>());
                }
                return __ctx.toDraftList((__modified!= null ? __modified : __base).types(), Type.class, true);
            }

            @Override
            public UserDraft setTypes(List<Type> types) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (types == null) {
                    throw new IllegalArgumentException(
                        "'types' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__typesValue = NonSharedList.of(__tmpModified.__typesValue, types);
                return this;
            }

            @Override
            public UserDraft addIntoTypes(DraftConsumer<TypeDraft> block) {
                addIntoTypes(null, block);
                return this;
            }

            @Override
            public UserDraft addIntoTypes(Type base, DraftConsumer<TypeDraft> block) {
                types(true).add((TypeDraft)TypeDraft.$.produce(base, block));
                return this;
            }

            @Override
            @JsonIgnore
            public List<User> adores() {
                return __ctx.toDraftList((__modified!= null ? __modified : __base).adores(), User.class, true);
            }

            @Override
            public List<UserDraft> adores(boolean autoCreate) {
                if (autoCreate && (!__isLoaded(PropId.byIndex(SLOT_ADORES)))) {
                    setAdores(new ArrayList<>());
                }
                return __ctx.toDraftList((__modified!= null ? __modified : __base).adores(), User.class, true);
            }

            @Override
            public UserDraft setAdores(List<User> adores) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (adores == null) {
                    throw new IllegalArgumentException(
                        "'adores' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__adoresValue = NonSharedList.of(__tmpModified.__adoresValue, adores);
                return this;
            }

            @Override
            public UserDraft addIntoAdores(DraftConsumer<UserDraft> block) {
                addIntoAdores(null, block);
                return this;
            }

            @Override
            public UserDraft addIntoAdores(User base, DraftConsumer<UserDraft> block) {
                adores(true).add((UserDraft)UserDraft.$.produce(base, block));
                return this;
            }

            @Override
            @JsonIgnore
            public List<User> followers() {
                return __ctx.toDraftList((__modified!= null ? __modified : __base).followers(), User.class, true);
            }

            @Override
            public List<UserDraft> followers(boolean autoCreate) {
                if (autoCreate && (!__isLoaded(PropId.byIndex(SLOT_FOLLOWERS)))) {
                    setFollowers(new ArrayList<>());
                }
                return __ctx.toDraftList((__modified!= null ? __modified : __base).followers(), User.class, true);
            }

            @Override
            public UserDraft setFollowers(List<User> followers) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (followers == null) {
                    throw new IllegalArgumentException(
                        "'followers' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__followersValue = NonSharedList.of(__tmpModified.__followersValue, followers);
                return this;
            }

            @Override
            public UserDraft addIntoFollowers(DraftConsumer<UserDraft> block) {
                addIntoFollowers(null, block);
                return this;
            }

            @Override
            public UserDraft addIntoFollowers(User base, DraftConsumer<UserDraft> block) {
                followers(true).add((UserDraft)UserDraft.$.produce(base, block));
                return this;
            }

            @Override
            @JsonIgnore
            public List<Role> roles() {
                return __ctx.toDraftList((__modified!= null ? __modified : __base).roles(), Role.class, true);
            }

            @Override
            public List<RoleDraft> roles(boolean autoCreate) {
                if (autoCreate && (!__isLoaded(PropId.byIndex(SLOT_ROLES)))) {
                    setRoles(new ArrayList<>());
                }
                return __ctx.toDraftList((__modified!= null ? __modified : __base).roles(), Role.class, true);
            }

            @Override
            public UserDraft setRoles(List<Role> roles) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (roles == null) {
                    throw new IllegalArgumentException(
                        "'roles' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__rolesValue = NonSharedList.of(__tmpModified.__rolesValue, roles);
                return this;
            }

            @Override
            public UserDraft addIntoRoles(DraftConsumer<RoleDraft> block) {
                addIntoRoles(null, block);
                return this;
            }

            @Override
            public UserDraft addIntoRoles(Role base, DraftConsumer<RoleDraft> block) {
                roles(true).add((RoleDraft)RoleDraft.$.produce(base, block));
                return this;
            }

            @Override
            @JsonIgnore
            public List<Video> videos() {
                return __ctx.toDraftList((__modified!= null ? __modified : __base).videos(), Video.class, true);
            }

            @Override
            public List<VideoDraft> videos(boolean autoCreate) {
                if (autoCreate && (!__isLoaded(PropId.byIndex(SLOT_VIDEOS)))) {
                    setVideos(new ArrayList<>());
                }
                return __ctx.toDraftList((__modified!= null ? __modified : __base).videos(), Video.class, true);
            }

            @Override
            public UserDraft setVideos(List<Video> videos) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (videos == null) {
                    throw new IllegalArgumentException(
                        "'videos' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__videosValue = NonSharedList.of(__tmpModified.__videosValue, videos);
                return this;
            }

            @Override
            public UserDraft addIntoVideos(DraftConsumer<VideoDraft> block) {
                addIntoVideos(null, block);
                return this;
            }

            @Override
            public UserDraft addIntoVideos(Video base, DraftConsumer<VideoDraft> block) {
                videos(true).add((VideoDraft)VideoDraft.$.produce(base, block));
                return this;
            }

            @Override
            @JsonIgnore
            public List<Collection> collections() {
                return __ctx.toDraftList((__modified!= null ? __modified : __base).collections(), Collection.class, true);
            }

            @Override
            public List<CollectionDraft> collections(boolean autoCreate) {
                if (autoCreate && (!__isLoaded(PropId.byIndex(SLOT_COLLECTIONS)))) {
                    setCollections(new ArrayList<>());
                }
                return __ctx.toDraftList((__modified!= null ? __modified : __base).collections(), Collection.class, true);
            }

            @Override
            public UserDraft setCollections(List<Collection> collections) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (collections == null) {
                    throw new IllegalArgumentException(
                        "'collections' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__collectionsValue = NonSharedList.of(__tmpModified.__collectionsValue, collections);
                return this;
            }

            @Override
            public UserDraft addIntoCollections(DraftConsumer<CollectionDraft> block) {
                addIntoCollections(null, block);
                return this;
            }

            @Override
            public UserDraft addIntoCollections(Collection base,
                    DraftConsumer<CollectionDraft> block) {
                collections(true).add((CollectionDraft)CollectionDraft.$.produce(base, block));
                return this;
            }

            @Override
            @JsonIgnore
            public List<Long> typeIds() {
                List<Long> __ids = new ArrayList(types().size());
                for (Type __target : types()) {
                    __ids.add(__target.id());
                }
                return __ids;
            }

            @Override
            public List<Long> typeIds(boolean autoCreate) {
                if (autoCreate && (!__isLoaded(PropId.byIndex(SLOT_TYPES)))) {
                    setTypes(new ArrayList<>());
                }
                return new MutableIdViewList<>(TypeDraft.Producer.TYPE, types());
            }

            @Override
            public UserDraft setTypeIds(List<Long> typeIds) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (typeIds != null) {
                    List<Type> __targets = new ArrayList(typeIds.size());
                    for (long __id : typeIds) {
                        __targets.add(ImmutableObjects.makeIdOnly(Type.class, __id));
                    }
                    setTypes(__targets);
                } else {
                    setTypes(Collections.emptyList());
                }
                return this;
            }

            @Override
            @JsonIgnore
            public List<Long> collectedVideos() {
                return __ctx.toDraftList((__modified!= null ? __modified : __base).collectedVideos(), Long.class, false);
            }

            @Override
            @JsonIgnore
            public long adoresSum() {
                return (__modified!= null ? __modified : __base).adoresSum();
            }

            @Override
            @JsonIgnore
            public long followersSum() {
                return (__modified!= null ? __modified : __base).followersSum();
            }

            @Override
            @JsonIgnore
            public List<String> roleList() {
                return __ctx.toDraftList((__modified!= null ? __modified : __base).roleList(), String.class, false);
            }

            @Override
            @JsonIgnore
            public List<String> authorityList() {
                return __ctx.toDraftList((__modified!= null ? __modified : __base).authorityList(), String.class, false);
            }

            @Override
            @JsonIgnore
            public boolean userIsAdore() {
                return (__modified!= null ? __modified : __base).userIsAdore();
            }

            @SuppressWarnings("unchecked")
            @Override
            public void __set(PropId prop, Object value) {
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		__set(prop.asName(), value);
                    return;
                    case SLOT_CREATE_TIME:
                    		setCreateTime((Date)value);break;
                    case SLOT_MODIFY_TIME:
                    		setModifyTime((Date)value);break;
                    case SLOT_BETWEEN_TIME:
                    		break;
                    case SLOT_ID:
                    		if (value == null) throw new IllegalArgumentException("'id' cannot be null, if you want to set null, please use any annotation whose simple name is \"Nullable\" to decorate the property");
                            setId((Long)value);
                            break;
                    case SLOT_USERNAME:
                    		setUsername((String)value);break;
                    case SLOT_PASSWORD:
                    		setPassword((String)value);break;
                    case SLOT_EMAIL:
                    		setEmail((String)value);break;
                    case SLOT_DESCRIPTION:
                    		setDescription((String)value);break;
                    case SLOT_GENDER:
                    		setGender((String)value);break;
                    case SLOT_HOME:
                    		setHome((String)value);break;
                    case SLOT_STATUS:
                    		setStatus((String)value);break;
                    case SLOT_TYPES:
                    		setTypes((List<Type>)value);break;
                    case SLOT_ADORES:
                    		setAdores((List<User>)value);break;
                    case SLOT_FOLLOWERS:
                    		setFollowers((List<User>)value);break;
                    case SLOT_ROLES:
                    		setRoles((List<Role>)value);break;
                    case SLOT_VIDEOS:
                    		setVideos((List<Video>)value);break;
                    case SLOT_COLLECTIONS:
                    		setCollections((List<Collection>)value);break;
                    case SLOT_TYPE_IDS:
                    		setTypeIds((List<Long>)value);break;
                    case SLOT_COLLECTED_VIDEOS:
                    		break;
                    case SLOT_ADORES_SUM:
                    		break;
                    case SLOT_FOLLOWERS_SUM:
                    		break;
                    case SLOT_ROLE_LIST:
                    		break;
                    case SLOT_AUTHORITY_LIST:
                    		break;
                    case SLOT_USER_IS_ADORE:
                    		break;
                    default: throw new IllegalArgumentException("Illegal property id for \"com.celeste.entity.User\": \"" + prop + "\"");
                }
            }

            @SuppressWarnings("unchecked")
            @Override
            public void __set(String prop, Object value) {
                switch (prop) {
                    case "createTime":
                    		setCreateTime((Date)value);break;
                    case "modifyTime":
                    		setModifyTime((Date)value);break;
                    case "betweenTime":
                    		break;
                    case "id":
                    		if (value == null) throw new IllegalArgumentException("'id' cannot be null, if you want to set null, please use any annotation whose simple name is \"Nullable\" to decorate the property");
                            setId((Long)value);
                            break;
                    case "username":
                    		setUsername((String)value);break;
                    case "password":
                    		setPassword((String)value);break;
                    case "email":
                    		setEmail((String)value);break;
                    case "description":
                    		setDescription((String)value);break;
                    case "gender":
                    		setGender((String)value);break;
                    case "home":
                    		setHome((String)value);break;
                    case "status":
                    		setStatus((String)value);break;
                    case "types":
                    		setTypes((List<Type>)value);break;
                    case "adores":
                    		setAdores((List<User>)value);break;
                    case "followers":
                    		setFollowers((List<User>)value);break;
                    case "roles":
                    		setRoles((List<Role>)value);break;
                    case "videos":
                    		setVideos((List<Video>)value);break;
                    case "collections":
                    		setCollections((List<Collection>)value);break;
                    case "typeIds":
                    		setTypeIds((List<Long>)value);break;
                    case "collectedVideos":
                    		break;
                    case "adoresSum":
                    		break;
                    case "followersSum":
                    		break;
                    case "roleList":
                    		break;
                    case "authorityList":
                    		break;
                    case "userIsAdore":
                    		break;
                    default: throw new IllegalArgumentException("Illegal property name for \"com.celeste.entity.User\": \"" + prop + "\"");
                }
            }

            @Override
            public void __show(PropId prop, boolean visible) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                Visibility __visibility = (__modified!= null ? __modified : __base).__visibility;
                if (__visibility == null) {
                    if (visible) {
                        return;
                    }
                    __modified().__visibility = __visibility = Visibility.of(24);
                }
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		__show(prop.asName(), visible);
                    return;
                    case SLOT_CREATE_TIME:
                    		__visibility.show(SLOT_CREATE_TIME, visible);break;
                    case SLOT_MODIFY_TIME:
                    		__visibility.show(SLOT_MODIFY_TIME, visible);break;
                    case SLOT_BETWEEN_TIME:
                    		__visibility.show(SLOT_BETWEEN_TIME, visible);break;
                    case SLOT_ID:
                    		__visibility.show(SLOT_ID, visible);break;
                    case SLOT_USERNAME:
                    		__visibility.show(SLOT_USERNAME, visible);break;
                    case SLOT_PASSWORD:
                    		__visibility.show(SLOT_PASSWORD, visible);break;
                    case SLOT_EMAIL:
                    		__visibility.show(SLOT_EMAIL, visible);break;
                    case SLOT_DESCRIPTION:
                    		__visibility.show(SLOT_DESCRIPTION, visible);break;
                    case SLOT_GENDER:
                    		__visibility.show(SLOT_GENDER, visible);break;
                    case SLOT_HOME:
                    		__visibility.show(SLOT_HOME, visible);break;
                    case SLOT_STATUS:
                    		__visibility.show(SLOT_STATUS, visible);break;
                    case SLOT_TYPES:
                    		__visibility.show(SLOT_TYPES, visible);break;
                    case SLOT_ADORES:
                    		__visibility.show(SLOT_ADORES, visible);break;
                    case SLOT_FOLLOWERS:
                    		__visibility.show(SLOT_FOLLOWERS, visible);break;
                    case SLOT_ROLES:
                    		__visibility.show(SLOT_ROLES, visible);break;
                    case SLOT_VIDEOS:
                    		__visibility.show(SLOT_VIDEOS, visible);break;
                    case SLOT_COLLECTIONS:
                    		__visibility.show(SLOT_COLLECTIONS, visible);break;
                    case SLOT_TYPE_IDS:
                    		__visibility.show(SLOT_TYPE_IDS, visible);break;
                    case SLOT_COLLECTED_VIDEOS:
                    		__visibility.show(SLOT_COLLECTED_VIDEOS, visible);break;
                    case SLOT_ADORES_SUM:
                    		__visibility.show(SLOT_ADORES_SUM, visible);break;
                    case SLOT_FOLLOWERS_SUM:
                    		__visibility.show(SLOT_FOLLOWERS_SUM, visible);break;
                    case SLOT_ROLE_LIST:
                    		__visibility.show(SLOT_ROLE_LIST, visible);break;
                    case SLOT_AUTHORITY_LIST:
                    		__visibility.show(SLOT_AUTHORITY_LIST, visible);break;
                    case SLOT_USER_IS_ADORE:
                    		__visibility.show(SLOT_USER_IS_ADORE, visible);break;
                    default: throw new IllegalArgumentException(
                                "Illegal property id for \"com.celeste.entity.User\": \"" + 
                                prop + 
                                "\",it does not exists"
                            );
                }
            }

            @Override
            public void __show(String prop, boolean visible) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                Visibility __visibility = (__modified!= null ? __modified : __base).__visibility;
                if (__visibility == null) {
                    if (visible) {
                        return;
                    }
                    __modified().__visibility = __visibility = Visibility.of(24);
                }
                switch (prop) {
                    case "createTime":
                    		__visibility.show(SLOT_CREATE_TIME, visible);break;
                    case "modifyTime":
                    		__visibility.show(SLOT_MODIFY_TIME, visible);break;
                    case "betweenTime":
                    		__visibility.show(SLOT_BETWEEN_TIME, visible);break;
                    case "id":
                    		__visibility.show(SLOT_ID, visible);break;
                    case "username":
                    		__visibility.show(SLOT_USERNAME, visible);break;
                    case "password":
                    		__visibility.show(SLOT_PASSWORD, visible);break;
                    case "email":
                    		__visibility.show(SLOT_EMAIL, visible);break;
                    case "description":
                    		__visibility.show(SLOT_DESCRIPTION, visible);break;
                    case "gender":
                    		__visibility.show(SLOT_GENDER, visible);break;
                    case "home":
                    		__visibility.show(SLOT_HOME, visible);break;
                    case "status":
                    		__visibility.show(SLOT_STATUS, visible);break;
                    case "types":
                    		__visibility.show(SLOT_TYPES, visible);break;
                    case "adores":
                    		__visibility.show(SLOT_ADORES, visible);break;
                    case "followers":
                    		__visibility.show(SLOT_FOLLOWERS, visible);break;
                    case "roles":
                    		__visibility.show(SLOT_ROLES, visible);break;
                    case "videos":
                    		__visibility.show(SLOT_VIDEOS, visible);break;
                    case "collections":
                    		__visibility.show(SLOT_COLLECTIONS, visible);break;
                    case "typeIds":
                    		__visibility.show(SLOT_TYPE_IDS, visible);break;
                    case "collectedVideos":
                    		__visibility.show(SLOT_COLLECTED_VIDEOS, visible);break;
                    case "adoresSum":
                    		__visibility.show(SLOT_ADORES_SUM, visible);break;
                    case "followersSum":
                    		__visibility.show(SLOT_FOLLOWERS_SUM, visible);break;
                    case "roleList":
                    		__visibility.show(SLOT_ROLE_LIST, visible);break;
                    case "authorityList":
                    		__visibility.show(SLOT_AUTHORITY_LIST, visible);break;
                    case "userIsAdore":
                    		__visibility.show(SLOT_USER_IS_ADORE, visible);break;
                    default: throw new IllegalArgumentException(
                                "Illegal property name for \"com.celeste.entity.User\": \"" + 
                                prop + 
                                "\",it does not exists"
                            );
                }
            }

            @Override
            public void __unload(PropId prop) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		__unload(prop.asName());
                    return;
                    case SLOT_CREATE_TIME:
                    		__modified().__createTimeValue = null;break;
                    case SLOT_MODIFY_TIME:
                    		__modified().__modifyTimeLoaded = false;break;
                    case SLOT_BETWEEN_TIME:
                    		break;
                    case SLOT_ID:
                    		__modified().__idLoaded = false;break;
                    case SLOT_USERNAME:
                    		__modified().__usernameValue = null;break;
                    case SLOT_PASSWORD:
                    		__modified().__passwordValue = null;break;
                    case SLOT_EMAIL:
                    		__modified().__emailValue = null;break;
                    case SLOT_DESCRIPTION:
                    		__modified().__descriptionLoaded = false;break;
                    case SLOT_GENDER:
                    		__modified().__genderValue = null;break;
                    case SLOT_HOME:
                    		__modified().__homeValue = null;break;
                    case SLOT_STATUS:
                    		__modified().__statusValue = null;break;
                    case SLOT_TYPES:
                    		__modified().__typesValue = null;break;
                    case SLOT_ADORES:
                    		__modified().__adoresValue = null;break;
                    case SLOT_FOLLOWERS:
                    		__modified().__followersValue = null;break;
                    case SLOT_ROLES:
                    		__modified().__rolesValue = null;break;
                    case SLOT_VIDEOS:
                    		__modified().__videosValue = null;break;
                    case SLOT_COLLECTIONS:
                    		__modified().__collectionsValue = null;break;
                    case SLOT_TYPE_IDS:
                    		__unload(PropId.byIndex(SLOT_TYPES));break;
                    case SLOT_COLLECTED_VIDEOS:
                    		break;
                    case SLOT_ADORES_SUM:
                    		break;
                    case SLOT_FOLLOWERS_SUM:
                    		break;
                    case SLOT_ROLE_LIST:
                    		break;
                    case SLOT_AUTHORITY_LIST:
                    		break;
                    case SLOT_USER_IS_ADORE:
                    		break;
                    default: throw new IllegalArgumentException("Illegal property id for \"com.celeste.entity.User\": \"" + prop + "\", it does not exist or its loaded state is not controllable");
                }
            }

            @Override
            public void __unload(String prop) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                switch (prop) {
                    case "createTime":
                    		__modified().__createTimeValue = null;break;
                    case "modifyTime":
                    		__modified().__modifyTimeLoaded = false;break;
                    case "betweenTime":
                    		break;
                    case "id":
                    		__modified().__idLoaded = false;break;
                    case "username":
                    		__modified().__usernameValue = null;break;
                    case "password":
                    		__modified().__passwordValue = null;break;
                    case "email":
                    		__modified().__emailValue = null;break;
                    case "description":
                    		__modified().__descriptionLoaded = false;break;
                    case "gender":
                    		__modified().__genderValue = null;break;
                    case "home":
                    		__modified().__homeValue = null;break;
                    case "status":
                    		__modified().__statusValue = null;break;
                    case "types":
                    		__modified().__typesValue = null;break;
                    case "adores":
                    		__modified().__adoresValue = null;break;
                    case "followers":
                    		__modified().__followersValue = null;break;
                    case "roles":
                    		__modified().__rolesValue = null;break;
                    case "videos":
                    		__modified().__videosValue = null;break;
                    case "collections":
                    		__modified().__collectionsValue = null;break;
                    case "typeIds":
                    		__unload(PropId.byIndex(SLOT_TYPES));break;
                    case "collectedVideos":
                    		break;
                    case "adoresSum":
                    		break;
                    case "followersSum":
                    		break;
                    case "roleList":
                    		break;
                    case "authorityList":
                    		break;
                    case "userIsAdore":
                    		break;
                    default: throw new IllegalArgumentException("Illegal property name for \"com.celeste.entity.User\": \"" + prop + "\", it does not exist or its loaded state is not controllable");
                }
            }

            @Override
            public DraftContext __draftContext() {
                return __ctx;
            }

            @Override
            public Object __resolve() {
                if (__resolved != null) {
                    return __resolved;
                }
                if (__resolving) {
                    throw new CircularReferenceException();
                }
                __resolving = true;
                try {
                    Implementor base = __base;
                    Impl __tmpModified = __modified;
                    if (__tmpModified == null) {
                        if (base.__isLoaded(PropId.byIndex(SLOT_TYPES))) {
                            List<Type> oldValue = base.types();
                            List<Type> newValue = __ctx.resolveList(oldValue);
                            if (oldValue != newValue) {
                                setTypes(newValue);
                            }
                        }
                        if (base.__isLoaded(PropId.byIndex(SLOT_ADORES))) {
                            List<User> oldValue = base.adores();
                            List<User> newValue = __ctx.resolveList(oldValue);
                            if (oldValue != newValue) {
                                setAdores(newValue);
                            }
                        }
                        if (base.__isLoaded(PropId.byIndex(SLOT_FOLLOWERS))) {
                            List<User> oldValue = base.followers();
                            List<User> newValue = __ctx.resolveList(oldValue);
                            if (oldValue != newValue) {
                                setFollowers(newValue);
                            }
                        }
                        if (base.__isLoaded(PropId.byIndex(SLOT_ROLES))) {
                            List<Role> oldValue = base.roles();
                            List<Role> newValue = __ctx.resolveList(oldValue);
                            if (oldValue != newValue) {
                                setRoles(newValue);
                            }
                        }
                        if (base.__isLoaded(PropId.byIndex(SLOT_VIDEOS))) {
                            List<Video> oldValue = base.videos();
                            List<Video> newValue = __ctx.resolveList(oldValue);
                            if (oldValue != newValue) {
                                setVideos(newValue);
                            }
                        }
                        if (base.__isLoaded(PropId.byIndex(SLOT_COLLECTIONS))) {
                            List<Collection> oldValue = base.collections();
                            List<Collection> newValue = __ctx.resolveList(oldValue);
                            if (oldValue != newValue) {
                                setCollections(newValue);
                            }
                        }
                        __tmpModified = __modified;
                    }
                    else {
                        __tmpModified.__typesValue = NonSharedList.of(__tmpModified.__typesValue, __ctx.resolveList(__tmpModified.__typesValue));
                        __tmpModified.__adoresValue = NonSharedList.of(__tmpModified.__adoresValue, __ctx.resolveList(__tmpModified.__adoresValue));
                        __tmpModified.__followersValue = NonSharedList.of(__tmpModified.__followersValue, __ctx.resolveList(__tmpModified.__followersValue));
                        __tmpModified.__rolesValue = NonSharedList.of(__tmpModified.__rolesValue, __ctx.resolveList(__tmpModified.__rolesValue));
                        __tmpModified.__videosValue = NonSharedList.of(__tmpModified.__videosValue, __ctx.resolveList(__tmpModified.__videosValue));
                        __tmpModified.__collectionsValue = NonSharedList.of(__tmpModified.__collectionsValue, __ctx.resolveList(__tmpModified.__collectionsValue));
                    }
                    if (__base != null && __tmpModified == null) {
                        this.__resolved = base;
                        return base;
                    }
                    this.__resolved = __tmpModified;
                    return __tmpModified;
                }
                finally {
                    __resolving = false;
                }
            }

            @Override
            public boolean __isResolved() {
                return __resolved != null;
            }

            Impl __modified() {
                Impl __tmpModified = __modified;
                if (__tmpModified == null) {
                    __tmpModified = __base.clone();
                    __modified = __tmpModified;
                }
                return __tmpModified;
            }
        }
    }

    @GeneratedBy(
            type = User.class
    )
    class Builder {
        private final Producer.DraftImpl __draft;

        public Builder() {
            __draft = new Producer.DraftImpl(null, null);
            __draft.__show(PropId.byIndex(Producer.SLOT_ID), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_CREATE_TIME), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_BETWEEN_TIME), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_TYPES), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_ADORES), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_FOLLOWERS), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_ROLES), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_COLLECTIONS), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_TYPE_IDS), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_COLLECTED_VIDEOS), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_ADORES_SUM), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_FOLLOWERS_SUM), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_ROLE_LIST), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_AUTHORITY_LIST), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_USER_IS_ADORE), false);
        }

        public Builder id(Long id) {
            if (id != null) {
                __draft.setId(id);
                __draft.__show(PropId.byIndex(Producer.SLOT_ID), true);
            }
            return this;
        }

        public Builder createTime(Date createTime) {
            if (createTime != null) {
                __draft.setCreateTime(createTime);
                __draft.__show(PropId.byIndex(Producer.SLOT_CREATE_TIME), true);
            }
            return this;
        }

        @Nullable
        public Builder modifyTime(Date modifyTime) {
            __draft.setModifyTime(modifyTime);
            return this;
        }

        public Builder username(String username) {
            if (username != null) {
                __draft.setUsername(username);
            }
            return this;
        }

        public Builder password(String password) {
            if (password != null) {
                __draft.setPassword(password);
            }
            return this;
        }

        public Builder email(String email) {
            if (email != null) {
                __draft.setEmail(email);
            }
            return this;
        }

        @Nullable
        public Builder description(String description) {
            __draft.setDescription(description);
            return this;
        }

        public Builder gender(String gender) {
            if (gender != null) {
                __draft.setGender(gender);
            }
            return this;
        }

        public Builder home(String home) {
            if (home != null) {
                __draft.setHome(home);
            }
            return this;
        }

        public Builder status(String status) {
            if (status != null) {
                __draft.setStatus(status);
            }
            return this;
        }

        public Builder types(List<Type> types) {
            if (types != null) {
                __draft.setTypes(types);
                __draft.__show(PropId.byIndex(Producer.SLOT_TYPES), true);
            }
            return this;
        }

        public Builder adores(List<User> adores) {
            if (adores != null) {
                __draft.setAdores(adores);
                __draft.__show(PropId.byIndex(Producer.SLOT_ADORES), true);
            }
            return this;
        }

        public Builder followers(List<User> followers) {
            if (followers != null) {
                __draft.setFollowers(followers);
                __draft.__show(PropId.byIndex(Producer.SLOT_FOLLOWERS), true);
            }
            return this;
        }

        public Builder roles(List<Role> roles) {
            if (roles != null) {
                __draft.setRoles(roles);
                __draft.__show(PropId.byIndex(Producer.SLOT_ROLES), true);
            }
            return this;
        }

        public Builder videos(List<Video> videos) {
            if (videos != null) {
                __draft.setVideos(videos);
            }
            return this;
        }

        public Builder collections(List<Collection> collections) {
            if (collections != null) {
                __draft.setCollections(collections);
                __draft.__show(PropId.byIndex(Producer.SLOT_COLLECTIONS), true);
            }
            return this;
        }

        public Builder typeIds(List<Long> typeIds) {
            if (typeIds != null) {
                __draft.setTypeIds(typeIds);
                __draft.__show(PropId.byIndex(Producer.SLOT_TYPE_IDS), true);
            }
            return this;
        }

        public User build() {
            return (User)__draft.__modified();
        }
    }
}
