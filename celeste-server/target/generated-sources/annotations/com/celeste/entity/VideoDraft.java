package com.celeste.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.io.Serializable;
import java.lang.Boolean;
import java.lang.CloneNotSupportedException;
import java.lang.Cloneable;
import java.lang.IllegalArgumentException;
import java.lang.IllegalStateException;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.lang.System;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import org.babyfish.jimmer.CircularReferenceException;
import org.babyfish.jimmer.DraftConsumer;
import org.babyfish.jimmer.ImmutableObjects;
import org.babyfish.jimmer.UnloadedException;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.jackson.ImmutableModuleRequiredException;
import org.babyfish.jimmer.lang.OldChain;
import org.babyfish.jimmer.meta.ImmutablePropCategory;
import org.babyfish.jimmer.meta.ImmutableType;
import org.babyfish.jimmer.meta.PropId;
import org.babyfish.jimmer.runtime.DraftContext;
import org.babyfish.jimmer.runtime.DraftSpi;
import org.babyfish.jimmer.runtime.ImmutableSpi;
import org.babyfish.jimmer.runtime.Internal;
import org.babyfish.jimmer.runtime.NonSharedList;
import org.babyfish.jimmer.runtime.Visibility;
import org.babyfish.jimmer.sql.ManyToMany;
import org.babyfish.jimmer.sql.ManyToOne;
import org.babyfish.jimmer.sql.OneToMany;
import org.jetbrains.annotations.Nullable;

@GeneratedBy(
        type = Video.class
)
public interface VideoDraft extends Video, BaseDraft {
    VideoDraft.Producer $ = Producer.INSTANCE;

    @OldChain
    VideoDraft setId(long id);

    @OldChain
    VideoDraft setCreateTime(Date createTime);

    @OldChain
    VideoDraft setModifyTime(Date modifyTime);

    @OldChain
    VideoDraft setDescription(String description);

    @OldChain
    VideoDraft setStatus(String status);

    TypeDraft type();

    TypeDraft type(boolean autoCreate);

    @OldChain
    VideoDraft setType(Type type);

    long typeId();

    @OldChain
    VideoDraft setTypeId(long typeId);

    @OldChain
    VideoDraft applyType(DraftConsumer<TypeDraft> block);

    @OldChain
    VideoDraft applyType(Type base, DraftConsumer<TypeDraft> block);

    UserDraft user();

    UserDraft user(boolean autoCreate);

    @OldChain
    VideoDraft setUser(User user);

    long userId();

    @OldChain
    VideoDraft setUserId(long userId);

    @OldChain
    VideoDraft applyUser(DraftConsumer<UserDraft> block);

    @OldChain
    VideoDraft applyUser(User base, DraftConsumer<UserDraft> block);

    List<VideoLikeDraft> likes(boolean autoCreate);

    @OldChain
    VideoDraft setLikes(List<VideoLike> likes);

    @OldChain
    VideoDraft addIntoLikes(DraftConsumer<VideoLikeDraft> block);

    @OldChain
    VideoDraft addIntoLikes(VideoLike base, DraftConsumer<VideoLikeDraft> block);

    List<CommentDraft> comments(boolean autoCreate);

    @OldChain
    VideoDraft setComments(List<Comment> comments);

    @OldChain
    VideoDraft addIntoComments(DraftConsumer<CommentDraft> block);

    @OldChain
    VideoDraft addIntoComments(Comment base, DraftConsumer<CommentDraft> block);

    List<CollectionDraft> collections(boolean autoCreate);

    @OldChain
    VideoDraft setCollections(List<Collection> collections);

    @OldChain
    VideoDraft addIntoCollections(DraftConsumer<CollectionDraft> block);

    @OldChain
    VideoDraft addIntoCollections(Collection base, DraftConsumer<CollectionDraft> block);

    @GeneratedBy(
            type = Video.class
    )
    class Producer {
        static final Producer INSTANCE = new Producer();

        public static final int SLOT_ID = 3;

        public static final int SLOT_CREATE_TIME = 0;

        public static final int SLOT_MODIFY_TIME = 1;

        public static final int SLOT_BETWEEN_TIME = 2;

        public static final int SLOT_DESCRIPTION = 4;

        public static final int SLOT_STATUS = 5;

        public static final int SLOT_TYPE = 6;

        public static final int SLOT_USER = 7;

        public static final int SLOT_LIKES = 8;

        public static final int SLOT_COMMENTS = 9;

        public static final int SLOT_COLLECTIONS = 10;

        public static final int SLOT_SOURCE = 11;

        public static final int SLOT_POSTER = 12;

        public static final int SLOT_LIKES_SUM = 13;

        public static final int SLOT_COMMENTS_SUM = 14;

        public static final int SLOT_COLLECTIONS_SUM = 15;

        public static final int SLOT_USER_IS_ADORE = 16;

        public static final int SLOT_USER_IS_LIKE = 17;

        public static final int SLOT_USER_IS_COLLECT = 18;

        public static final ImmutableType TYPE = ImmutableType
            .newBuilder(
                "0.8.150",
                Video.class,
                Collections.singleton(BaseDraft.Producer.TYPE),
                (ctx, base) -> new DraftImpl(ctx, (Video)base)
            )
            .redefine("createTime", SLOT_CREATE_TIME)
            .redefine("modifyTime", SLOT_MODIFY_TIME)
            .redefine("betweenTime", SLOT_BETWEEN_TIME)
            .id(SLOT_ID, "id", long.class)
            .add(SLOT_DESCRIPTION, "description", ImmutablePropCategory.SCALAR, String.class, false)
            .add(SLOT_STATUS, "status", ImmutablePropCategory.SCALAR, String.class, false)
            .add(SLOT_TYPE, "type", ManyToOne.class, Type.class, false)
            .add(SLOT_USER, "user", ManyToOne.class, User.class, false)
            .add(SLOT_LIKES, "likes", OneToMany.class, VideoLike.class, false)
            .add(SLOT_COMMENTS, "comments", OneToMany.class, Comment.class, false)
            .add(SLOT_COLLECTIONS, "collections", ManyToMany.class, Collection.class, false)
            .add(SLOT_SOURCE, "source", ImmutablePropCategory.SCALAR, String.class, false)
            .add(SLOT_POSTER, "poster", ImmutablePropCategory.SCALAR, String.class, false)
            .add(SLOT_LIKES_SUM, "likesSum", ImmutablePropCategory.SCALAR, long.class, false)
            .add(SLOT_COMMENTS_SUM, "commentsSum", ImmutablePropCategory.SCALAR, long.class, false)
            .add(SLOT_COLLECTIONS_SUM, "collectionsSum", ImmutablePropCategory.SCALAR, long.class, false)
            .add(SLOT_USER_IS_ADORE, "userIsAdore", ImmutablePropCategory.SCALAR, boolean.class, false)
            .add(SLOT_USER_IS_LIKE, "userIsLike", ImmutablePropCategory.SCALAR, boolean.class, false)
            .add(SLOT_USER_IS_COLLECT, "userIsCollect", ImmutablePropCategory.SCALAR, boolean.class, false)
            .build();

        private Producer() {
        }

        public Video produce(DraftConsumer<VideoDraft> block) {
            return produce(null, block);
        }

        public Video produce(Video base, DraftConsumer<VideoDraft> block) {
            return (Video)Internal.produce(TYPE, base, block);
        }

        @GeneratedBy(
                type = Video.class
        )
        @JsonPropertyOrder({"dummyPropForJacksonError__", "createTime", "modifyTime", "betweenTime", "id", "description", "status", "type", "user", "likes", "comments", "collections", "source", "poster", "likesSum", "commentsSum", "collectionsSum", "userIsAdore", "userIsLike", "userIsCollect"})
        public abstract interface Implementor extends Video, ImmutableSpi {
            @Override
            default Object __get(PropId prop) {
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		return __get(prop.asName());
                    case SLOT_CREATE_TIME:
                    		return createTime();
                    case SLOT_MODIFY_TIME:
                    		return modifyTime();
                    case SLOT_BETWEEN_TIME:
                    		return betweenTime();
                    case SLOT_ID:
                    		return (Long)id();
                    case SLOT_DESCRIPTION:
                    		return description();
                    case SLOT_STATUS:
                    		return status();
                    case SLOT_TYPE:
                    		return type();
                    case SLOT_USER:
                    		return user();
                    case SLOT_LIKES:
                    		return likes();
                    case SLOT_COMMENTS:
                    		return comments();
                    case SLOT_COLLECTIONS:
                    		return collections();
                    case SLOT_SOURCE:
                    		return source();
                    case SLOT_POSTER:
                    		return poster();
                    case SLOT_LIKES_SUM:
                    		return (Long)likesSum();
                    case SLOT_COMMENTS_SUM:
                    		return (Long)commentsSum();
                    case SLOT_COLLECTIONS_SUM:
                    		return (Long)collectionsSum();
                    case SLOT_USER_IS_ADORE:
                    		return (Boolean)userIsAdore();
                    case SLOT_USER_IS_LIKE:
                    		return (Boolean)userIsLike();
                    case SLOT_USER_IS_COLLECT:
                    		return (Boolean)userIsCollect();
                    default: throw new IllegalArgumentException("Illegal property name for \"com.celeste.entity.Video\": \"" + prop + "\"");
                }
            }

            @Override
            default Object __get(String prop) {
                switch (prop) {
                    case "createTime":
                    		return createTime();
                    case "modifyTime":
                    		return modifyTime();
                    case "betweenTime":
                    		return betweenTime();
                    case "id":
                    		return (Long)id();
                    case "description":
                    		return description();
                    case "status":
                    		return status();
                    case "type":
                    		return type();
                    case "user":
                    		return user();
                    case "likes":
                    		return likes();
                    case "comments":
                    		return comments();
                    case "collections":
                    		return collections();
                    case "source":
                    		return source();
                    case "poster":
                    		return poster();
                    case "likesSum":
                    		return (Long)likesSum();
                    case "commentsSum":
                    		return (Long)commentsSum();
                    case "collectionsSum":
                    		return (Long)collectionsSum();
                    case "userIsAdore":
                    		return (Boolean)userIsAdore();
                    case "userIsLike":
                    		return (Boolean)userIsLike();
                    case "userIsCollect":
                    		return (Boolean)userIsCollect();
                    default: throw new IllegalArgumentException("Illegal property name for \"com.celeste.entity.Video\": \"" + prop + "\"");
                }
            }

            default long getId() {
                return id();
            }

            default Date getCreateTime() {
                return createTime();
            }

            @Nullable
            default Date getModifyTime() {
                return modifyTime();
            }

            default String getBetweenTime() {
                return betweenTime();
            }

            default String getDescription() {
                return description();
            }

            default String getStatus() {
                return status();
            }

            default Type getType() {
                return type();
            }

            default User getUser() {
                return user();
            }

            default List<VideoLike> getLikes() {
                return likes();
            }

            default List<Comment> getComments() {
                return comments();
            }

            default List<Collection> getCollections() {
                return collections();
            }

            default String getSource() {
                return source();
            }

            default String getPoster() {
                return poster();
            }

            default long getLikesSum() {
                return likesSum();
            }

            default long getCommentsSum() {
                return commentsSum();
            }

            default long getCollectionsSum() {
                return collectionsSum();
            }

            default boolean isUserIsAdore() {
                return userIsAdore();
            }

            default boolean isUserIsLike() {
                return userIsLike();
            }

            default boolean isUserIsCollect() {
                return userIsCollect();
            }

            @Override
            default ImmutableType __type() {
                return TYPE;
            }

            default int getDummyPropForJacksonError__() {
                throw new ImmutableModuleRequiredException();
            }
        }

        @GeneratedBy(
                type = Video.class
        )
        private static class Impl implements Implementor, Cloneable, Serializable {
            private Visibility __visibility;

            long __idValue;

            boolean __idLoaded = false;

            Date __createTimeValue;

            Date __modifyTimeValue;

            boolean __modifyTimeLoaded = false;

            String __descriptionValue;

            String __statusValue;

            Type __typeValue;

            User __userValue;

            NonSharedList<VideoLike> __likesValue;

            NonSharedList<Comment> __commentsValue;

            NonSharedList<Collection> __collectionsValue;

            Impl() {
                __visibility = Visibility.of(19);
                __visibility.show(SLOT_BETWEEN_TIME, false);
                __visibility.show(SLOT_SOURCE, false);
                __visibility.show(SLOT_POSTER, false);
                __visibility.show(SLOT_LIKES_SUM, false);
                __visibility.show(SLOT_COMMENTS_SUM, false);
                __visibility.show(SLOT_COLLECTIONS_SUM, false);
                __visibility.show(SLOT_USER_IS_ADORE, false);
                __visibility.show(SLOT_USER_IS_LIKE, false);
                __visibility.show(SLOT_USER_IS_COLLECT, false);
            }

            @Override
            @JsonIgnore
            public long id() {
                if (!__idLoaded) {
                    throw new UnloadedException(Video.class, "id");
                }
                return __idValue;
            }

            @Override
            @JsonIgnore
            public Date createTime() {
                if (__createTimeValue == null) {
                    throw new UnloadedException(Video.class, "createTime");
                }
                return __createTimeValue;
            }

            @Override
            @JsonIgnore
            @Nullable
            public Date modifyTime() {
                if (!__modifyTimeLoaded) {
                    throw new UnloadedException(Video.class, "modifyTime");
                }
                return __modifyTimeValue;
            }

            @Override
            @JsonIgnore
            public String description() {
                if (__descriptionValue == null) {
                    throw new UnloadedException(Video.class, "description");
                }
                return __descriptionValue;
            }

            @Override
            @JsonIgnore
            public String status() {
                if (__statusValue == null) {
                    throw new UnloadedException(Video.class, "status");
                }
                return __statusValue;
            }

            @Override
            @JsonIgnore
            public Type type() {
                if (__typeValue == null) {
                    throw new UnloadedException(Video.class, "type");
                }
                return __typeValue;
            }

            @Override
            @JsonIgnore
            public User user() {
                if (__userValue == null) {
                    throw new UnloadedException(Video.class, "user");
                }
                return __userValue;
            }

            @Override
            @JsonIgnore
            public List<VideoLike> likes() {
                if (__likesValue == null) {
                    throw new UnloadedException(Video.class, "likes");
                }
                return __likesValue;
            }

            @Override
            @JsonIgnore
            public List<Comment> comments() {
                if (__commentsValue == null) {
                    throw new UnloadedException(Video.class, "comments");
                }
                return __commentsValue;
            }

            @Override
            @JsonIgnore
            public List<Collection> collections() {
                if (__collectionsValue == null) {
                    throw new UnloadedException(Video.class, "collections");
                }
                return __collectionsValue;
            }

            @Override
            public Impl clone() {
                try {
                    return (Impl)super.clone();
                } catch(CloneNotSupportedException ex) {
                    throw new AssertionError(ex);
                }
            }

            @Override
            public boolean __isLoaded(PropId prop) {
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		return __isLoaded(prop.asName());
                    case SLOT_CREATE_TIME:
                    		return __createTimeValue != null;
                    case SLOT_MODIFY_TIME:
                    		return __modifyTimeLoaded;
                    case SLOT_BETWEEN_TIME:
                    		return __isLoaded(PropId.byIndex(SLOT_CREATE_TIME));
                    case SLOT_ID:
                    		return __idLoaded;
                    case SLOT_DESCRIPTION:
                    		return __descriptionValue != null;
                    case SLOT_STATUS:
                    		return __statusValue != null;
                    case SLOT_TYPE:
                    		return __typeValue != null;
                    case SLOT_USER:
                    		return __userValue != null;
                    case SLOT_LIKES:
                    		return __likesValue != null;
                    case SLOT_COMMENTS:
                    		return __commentsValue != null;
                    case SLOT_COLLECTIONS:
                    		return __collectionsValue != null;
                    case SLOT_SOURCE:
                    		return __isLoaded(PropId.byIndex(SLOT_ID));
                    case SLOT_POSTER:
                    		return __isLoaded(PropId.byIndex(SLOT_ID));
                    case SLOT_LIKES_SUM:
                    		return __isLoaded(PropId.byIndex(SLOT_LIKES));
                    case SLOT_COMMENTS_SUM:
                    		return __isLoaded(PropId.byIndex(SLOT_COMMENTS));
                    case SLOT_COLLECTIONS_SUM:
                    		return ImmutableObjects.isLoadedChain(this, PropId.byIndex(Producer.SLOT_COLLECTIONS), PropId.byIndex(CollectionDraft.Producer.SLOT_USER), PropId.byIndex(UserDraft.Producer.SLOT_ID));
                    case SLOT_USER_IS_ADORE:
                    		return ImmutableObjects.isLoadedChain(this, PropId.byIndex(Producer.SLOT_USER), PropId.byIndex(UserDraft.Producer.SLOT_FOLLOWERS), PropId.byIndex(UserDraft.Producer.SLOT_ID));
                    case SLOT_USER_IS_LIKE:
                    		return ImmutableObjects.isLoadedChain(this, PropId.byIndex(Producer.SLOT_LIKES), PropId.byIndex(VideoLikeDraft.Producer.SLOT_USER), PropId.byIndex(UserDraft.Producer.SLOT_ID));
                    case SLOT_USER_IS_COLLECT:
                    		return ImmutableObjects.isLoadedChain(this, PropId.byIndex(Producer.SLOT_COLLECTIONS), PropId.byIndex(CollectionDraft.Producer.SLOT_USER), PropId.byIndex(UserDraft.Producer.SLOT_ID));
                    default: throw new IllegalArgumentException("Illegal property name for \"com.celeste.entity.Video\": \"" + prop + "\"");
                }
            }

            @Override
            public boolean __isLoaded(String prop) {
                switch (prop) {
                    case "createTime":
                    		return __createTimeValue != null;
                    case "modifyTime":
                    		return __modifyTimeLoaded;
                    case "betweenTime":
                    		return __isLoaded(PropId.byIndex(SLOT_CREATE_TIME));
                    case "id":
                    		return __idLoaded;
                    case "description":
                    		return __descriptionValue != null;
                    case "status":
                    		return __statusValue != null;
                    case "type":
                    		return __typeValue != null;
                    case "user":
                    		return __userValue != null;
                    case "likes":
                    		return __likesValue != null;
                    case "comments":
                    		return __commentsValue != null;
                    case "collections":
                    		return __collectionsValue != null;
                    case "source":
                    		return __isLoaded(PropId.byIndex(SLOT_ID));
                    case "poster":
                    		return __isLoaded(PropId.byIndex(SLOT_ID));
                    case "likesSum":
                    		return __isLoaded(PropId.byIndex(SLOT_LIKES));
                    case "commentsSum":
                    		return __isLoaded(PropId.byIndex(SLOT_COMMENTS));
                    case "collectionsSum":
                    		return ImmutableObjects.isLoadedChain(this, PropId.byIndex(Producer.SLOT_COLLECTIONS), PropId.byIndex(CollectionDraft.Producer.SLOT_USER), PropId.byIndex(UserDraft.Producer.SLOT_ID));
                    case "userIsAdore":
                    		return ImmutableObjects.isLoadedChain(this, PropId.byIndex(Producer.SLOT_USER), PropId.byIndex(UserDraft.Producer.SLOT_FOLLOWERS), PropId.byIndex(UserDraft.Producer.SLOT_ID));
                    case "userIsLike":
                    		return ImmutableObjects.isLoadedChain(this, PropId.byIndex(Producer.SLOT_LIKES), PropId.byIndex(VideoLikeDraft.Producer.SLOT_USER), PropId.byIndex(UserDraft.Producer.SLOT_ID));
                    case "userIsCollect":
                    		return ImmutableObjects.isLoadedChain(this, PropId.byIndex(Producer.SLOT_COLLECTIONS), PropId.byIndex(CollectionDraft.Producer.SLOT_USER), PropId.byIndex(UserDraft.Producer.SLOT_ID));
                    default: throw new IllegalArgumentException("Illegal property name for \"com.celeste.entity.Video\": \"" + prop + "\"");
                }
            }

            @Override
            public boolean __isVisible(PropId prop) {
                if (__visibility == null) {
                    return true;
                }
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		return __isVisible(prop.asName());
                    case SLOT_CREATE_TIME:
                    		return __visibility.visible(SLOT_CREATE_TIME);
                    case SLOT_MODIFY_TIME:
                    		return __visibility.visible(SLOT_MODIFY_TIME);
                    case SLOT_BETWEEN_TIME:
                    		return __visibility.visible(SLOT_BETWEEN_TIME);
                    case SLOT_ID:
                    		return __visibility.visible(SLOT_ID);
                    case SLOT_DESCRIPTION:
                    		return __visibility.visible(SLOT_DESCRIPTION);
                    case SLOT_STATUS:
                    		return __visibility.visible(SLOT_STATUS);
                    case SLOT_TYPE:
                    		return __visibility.visible(SLOT_TYPE);
                    case SLOT_USER:
                    		return __visibility.visible(SLOT_USER);
                    case SLOT_LIKES:
                    		return __visibility.visible(SLOT_LIKES);
                    case SLOT_COMMENTS:
                    		return __visibility.visible(SLOT_COMMENTS);
                    case SLOT_COLLECTIONS:
                    		return __visibility.visible(SLOT_COLLECTIONS);
                    case SLOT_SOURCE:
                    		return __visibility.visible(SLOT_SOURCE);
                    case SLOT_POSTER:
                    		return __visibility.visible(SLOT_POSTER);
                    case SLOT_LIKES_SUM:
                    		return __visibility.visible(SLOT_LIKES_SUM);
                    case SLOT_COMMENTS_SUM:
                    		return __visibility.visible(SLOT_COMMENTS_SUM);
                    case SLOT_COLLECTIONS_SUM:
                    		return __visibility.visible(SLOT_COLLECTIONS_SUM);
                    case SLOT_USER_IS_ADORE:
                    		return __visibility.visible(SLOT_USER_IS_ADORE);
                    case SLOT_USER_IS_LIKE:
                    		return __visibility.visible(SLOT_USER_IS_LIKE);
                    case SLOT_USER_IS_COLLECT:
                    		return __visibility.visible(SLOT_USER_IS_COLLECT);
                    default: return true;
                }
            }

            @Override
            public boolean __isVisible(String prop) {
                if (__visibility == null) {
                    return true;
                }
                switch (prop) {
                    case "createTime":
                    		return __visibility.visible(SLOT_CREATE_TIME);
                    case "modifyTime":
                    		return __visibility.visible(SLOT_MODIFY_TIME);
                    case "betweenTime":
                    		return __visibility.visible(SLOT_BETWEEN_TIME);
                    case "id":
                    		return __visibility.visible(SLOT_ID);
                    case "description":
                    		return __visibility.visible(SLOT_DESCRIPTION);
                    case "status":
                    		return __visibility.visible(SLOT_STATUS);
                    case "type":
                    		return __visibility.visible(SLOT_TYPE);
                    case "user":
                    		return __visibility.visible(SLOT_USER);
                    case "likes":
                    		return __visibility.visible(SLOT_LIKES);
                    case "comments":
                    		return __visibility.visible(SLOT_COMMENTS);
                    case "collections":
                    		return __visibility.visible(SLOT_COLLECTIONS);
                    case "source":
                    		return __visibility.visible(SLOT_SOURCE);
                    case "poster":
                    		return __visibility.visible(SLOT_POSTER);
                    case "likesSum":
                    		return __visibility.visible(SLOT_LIKES_SUM);
                    case "commentsSum":
                    		return __visibility.visible(SLOT_COMMENTS_SUM);
                    case "collectionsSum":
                    		return __visibility.visible(SLOT_COLLECTIONS_SUM);
                    case "userIsAdore":
                    		return __visibility.visible(SLOT_USER_IS_ADORE);
                    case "userIsLike":
                    		return __visibility.visible(SLOT_USER_IS_LIKE);
                    case "userIsCollect":
                    		return __visibility.visible(SLOT_USER_IS_COLLECT);
                    default: return true;
                }
            }

            @Override
            public int hashCode() {
                int hash = __visibility != null ? __visibility.hashCode() : 0;
                if (__idLoaded) {
                    hash = 31 * hash + Long.hashCode(__idValue);
                    // If entity-id is loaded, return directly
                    return hash;
                }
                if (__createTimeValue != null) {
                    hash = 31 * hash + __createTimeValue.hashCode();
                }
                if (__modifyTimeLoaded && __modifyTimeValue != null) {
                    hash = 31 * hash + __modifyTimeValue.hashCode();
                }
                if (__descriptionValue != null) {
                    hash = 31 * hash + __descriptionValue.hashCode();
                }
                if (__statusValue != null) {
                    hash = 31 * hash + __statusValue.hashCode();
                }
                if (__typeValue != null) {
                    hash = 31 * hash + __typeValue.hashCode();
                }
                if (__userValue != null) {
                    hash = 31 * hash + __userValue.hashCode();
                }
                if (__likesValue != null) {
                    hash = 31 * hash + __likesValue.hashCode();
                }
                if (__commentsValue != null) {
                    hash = 31 * hash + __commentsValue.hashCode();
                }
                if (__collectionsValue != null) {
                    hash = 31 * hash + __collectionsValue.hashCode();
                }
                return hash;
            }

            private int __shallowHashCode() {
                int hash = __visibility != null ? __visibility.hashCode() : 0;
                if (__idLoaded) {
                    hash = 31 * hash + Long.hashCode(__idValue);
                }
                if (__createTimeValue != null) {
                    hash = 31 * hash + System.identityHashCode(__createTimeValue);
                }
                if (__modifyTimeLoaded) {
                    hash = 31 * hash + System.identityHashCode(__modifyTimeValue);
                }
                if (__descriptionValue != null) {
                    hash = 31 * hash + System.identityHashCode(__descriptionValue);
                }
                if (__statusValue != null) {
                    hash = 31 * hash + System.identityHashCode(__statusValue);
                }
                if (__typeValue != null) {
                    hash = 31 * hash + System.identityHashCode(__typeValue);
                }
                if (__userValue != null) {
                    hash = 31 * hash + System.identityHashCode(__userValue);
                }
                if (__likesValue != null) {
                    hash = 31 * hash + System.identityHashCode(__likesValue);
                }
                if (__commentsValue != null) {
                    hash = 31 * hash + System.identityHashCode(__commentsValue);
                }
                if (__collectionsValue != null) {
                    hash = 31 * hash + System.identityHashCode(__collectionsValue);
                }
                return hash;
            }

            @Override
            public int __hashCode(boolean shallow) {
                return shallow ? __shallowHashCode() : hashCode();
            }

            @Override
            public boolean equals(Object obj) {
                if (obj == null || !(obj instanceof Implementor)) {
                    return false;
                }
                Implementor __other = (Implementor)obj;
                if (__isVisible(PropId.byIndex(SLOT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ID))) {
                    return false;
                }
                boolean __idLoaded = this.__idLoaded;
                if (__idLoaded != __other.__isLoaded(PropId.byIndex(SLOT_ID))) {
                    return false;
                }
                if (__idLoaded) {
                    // If entity-id is loaded, return directly
                    return __idValue == __other.id();
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATE_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_CREATE_TIME))) {
                    return false;
                }
                boolean __createTimeLoaded = __createTimeValue != null;
                if (__createTimeLoaded != __other.__isLoaded(PropId.byIndex(SLOT_CREATE_TIME))) {
                    return false;
                }
                if (__createTimeLoaded && !Objects.equals(__createTimeValue, __other.createTime())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_MODIFY_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_MODIFY_TIME))) {
                    return false;
                }
                boolean __modifyTimeLoaded = this.__modifyTimeLoaded;
                if (__modifyTimeLoaded != __other.__isLoaded(PropId.byIndex(SLOT_MODIFY_TIME))) {
                    return false;
                }
                if (__modifyTimeLoaded && !Objects.equals(__modifyTimeValue, __other.modifyTime())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_BETWEEN_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_BETWEEN_TIME))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_DESCRIPTION)) != __other.__isVisible(PropId.byIndex(SLOT_DESCRIPTION))) {
                    return false;
                }
                boolean __descriptionLoaded = __descriptionValue != null;
                if (__descriptionLoaded != __other.__isLoaded(PropId.byIndex(SLOT_DESCRIPTION))) {
                    return false;
                }
                if (__descriptionLoaded && !Objects.equals(__descriptionValue, __other.description())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_STATUS)) != __other.__isVisible(PropId.byIndex(SLOT_STATUS))) {
                    return false;
                }
                boolean __statusLoaded = __statusValue != null;
                if (__statusLoaded != __other.__isLoaded(PropId.byIndex(SLOT_STATUS))) {
                    return false;
                }
                if (__statusLoaded && !Objects.equals(__statusValue, __other.status())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_TYPE)) != __other.__isVisible(PropId.byIndex(SLOT_TYPE))) {
                    return false;
                }
                boolean __typeLoaded = __typeValue != null;
                if (__typeLoaded != __other.__isLoaded(PropId.byIndex(SLOT_TYPE))) {
                    return false;
                }
                if (__typeLoaded && !Objects.equals(__typeValue, __other.type())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_USER)) != __other.__isVisible(PropId.byIndex(SLOT_USER))) {
                    return false;
                }
                boolean __userLoaded = __userValue != null;
                if (__userLoaded != __other.__isLoaded(PropId.byIndex(SLOT_USER))) {
                    return false;
                }
                if (__userLoaded && !Objects.equals(__userValue, __other.user())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_LIKES)) != __other.__isVisible(PropId.byIndex(SLOT_LIKES))) {
                    return false;
                }
                boolean __likesLoaded = __likesValue != null;
                if (__likesLoaded != __other.__isLoaded(PropId.byIndex(SLOT_LIKES))) {
                    return false;
                }
                if (__likesLoaded && !Objects.equals(__likesValue, __other.likes())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_COMMENTS)) != __other.__isVisible(PropId.byIndex(SLOT_COMMENTS))) {
                    return false;
                }
                boolean __commentsLoaded = __commentsValue != null;
                if (__commentsLoaded != __other.__isLoaded(PropId.byIndex(SLOT_COMMENTS))) {
                    return false;
                }
                if (__commentsLoaded && !Objects.equals(__commentsValue, __other.comments())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_COLLECTIONS)) != __other.__isVisible(PropId.byIndex(SLOT_COLLECTIONS))) {
                    return false;
                }
                boolean __collectionsLoaded = __collectionsValue != null;
                if (__collectionsLoaded != __other.__isLoaded(PropId.byIndex(SLOT_COLLECTIONS))) {
                    return false;
                }
                if (__collectionsLoaded && !Objects.equals(__collectionsValue, __other.collections())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_SOURCE)) != __other.__isVisible(PropId.byIndex(SLOT_SOURCE))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_POSTER)) != __other.__isVisible(PropId.byIndex(SLOT_POSTER))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_LIKES_SUM)) != __other.__isVisible(PropId.byIndex(SLOT_LIKES_SUM))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_COMMENTS_SUM)) != __other.__isVisible(PropId.byIndex(SLOT_COMMENTS_SUM))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_COLLECTIONS_SUM)) != __other.__isVisible(PropId.byIndex(SLOT_COLLECTIONS_SUM))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_USER_IS_ADORE)) != __other.__isVisible(PropId.byIndex(SLOT_USER_IS_ADORE))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_USER_IS_LIKE)) != __other.__isVisible(PropId.byIndex(SLOT_USER_IS_LIKE))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_USER_IS_COLLECT)) != __other.__isVisible(PropId.byIndex(SLOT_USER_IS_COLLECT))) {
                    return false;
                }
                return true;
            }

            private boolean __shallowEquals(Object obj) {
                if (obj == null || !(obj instanceof Implementor)) {
                    return false;
                }
                Implementor __other = (Implementor)obj;
                if (__isVisible(PropId.byIndex(SLOT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ID))) {
                    return false;
                }
                boolean __idLoaded = this.__idLoaded;
                if (__idLoaded != __other.__isLoaded(PropId.byIndex(SLOT_ID))) {
                    return false;
                }
                if (__idLoaded && __idValue != __other.id()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATE_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_CREATE_TIME))) {
                    return false;
                }
                boolean __createTimeLoaded = __createTimeValue != null;
                if (__createTimeLoaded != __other.__isLoaded(PropId.byIndex(SLOT_CREATE_TIME))) {
                    return false;
                }
                if (__createTimeLoaded && __createTimeValue != __other.createTime()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_MODIFY_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_MODIFY_TIME))) {
                    return false;
                }
                boolean __modifyTimeLoaded = this.__modifyTimeLoaded;
                if (__modifyTimeLoaded != __other.__isLoaded(PropId.byIndex(SLOT_MODIFY_TIME))) {
                    return false;
                }
                if (__modifyTimeLoaded && __modifyTimeValue != __other.modifyTime()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_BETWEEN_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_BETWEEN_TIME))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_DESCRIPTION)) != __other.__isVisible(PropId.byIndex(SLOT_DESCRIPTION))) {
                    return false;
                }
                boolean __descriptionLoaded = __descriptionValue != null;
                if (__descriptionLoaded != __other.__isLoaded(PropId.byIndex(SLOT_DESCRIPTION))) {
                    return false;
                }
                if (__descriptionLoaded && __descriptionValue != __other.description()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_STATUS)) != __other.__isVisible(PropId.byIndex(SLOT_STATUS))) {
                    return false;
                }
                boolean __statusLoaded = __statusValue != null;
                if (__statusLoaded != __other.__isLoaded(PropId.byIndex(SLOT_STATUS))) {
                    return false;
                }
                if (__statusLoaded && __statusValue != __other.status()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_TYPE)) != __other.__isVisible(PropId.byIndex(SLOT_TYPE))) {
                    return false;
                }
                boolean __typeLoaded = __typeValue != null;
                if (__typeLoaded != __other.__isLoaded(PropId.byIndex(SLOT_TYPE))) {
                    return false;
                }
                if (__typeLoaded && __typeValue != __other.type()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_USER)) != __other.__isVisible(PropId.byIndex(SLOT_USER))) {
                    return false;
                }
                boolean __userLoaded = __userValue != null;
                if (__userLoaded != __other.__isLoaded(PropId.byIndex(SLOT_USER))) {
                    return false;
                }
                if (__userLoaded && __userValue != __other.user()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_LIKES)) != __other.__isVisible(PropId.byIndex(SLOT_LIKES))) {
                    return false;
                }
                boolean __likesLoaded = __likesValue != null;
                if (__likesLoaded != __other.__isLoaded(PropId.byIndex(SLOT_LIKES))) {
                    return false;
                }
                if (__likesLoaded && __likesValue != __other.likes()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_COMMENTS)) != __other.__isVisible(PropId.byIndex(SLOT_COMMENTS))) {
                    return false;
                }
                boolean __commentsLoaded = __commentsValue != null;
                if (__commentsLoaded != __other.__isLoaded(PropId.byIndex(SLOT_COMMENTS))) {
                    return false;
                }
                if (__commentsLoaded && __commentsValue != __other.comments()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_COLLECTIONS)) != __other.__isVisible(PropId.byIndex(SLOT_COLLECTIONS))) {
                    return false;
                }
                boolean __collectionsLoaded = __collectionsValue != null;
                if (__collectionsLoaded != __other.__isLoaded(PropId.byIndex(SLOT_COLLECTIONS))) {
                    return false;
                }
                if (__collectionsLoaded && __collectionsValue != __other.collections()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_SOURCE)) != __other.__isVisible(PropId.byIndex(SLOT_SOURCE))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_POSTER)) != __other.__isVisible(PropId.byIndex(SLOT_POSTER))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_LIKES_SUM)) != __other.__isVisible(PropId.byIndex(SLOT_LIKES_SUM))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_COMMENTS_SUM)) != __other.__isVisible(PropId.byIndex(SLOT_COMMENTS_SUM))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_COLLECTIONS_SUM)) != __other.__isVisible(PropId.byIndex(SLOT_COLLECTIONS_SUM))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_USER_IS_ADORE)) != __other.__isVisible(PropId.byIndex(SLOT_USER_IS_ADORE))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_USER_IS_LIKE)) != __other.__isVisible(PropId.byIndex(SLOT_USER_IS_LIKE))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_USER_IS_COLLECT)) != __other.__isVisible(PropId.byIndex(SLOT_USER_IS_COLLECT))) {
                    return false;
                }
                return true;
            }

            @Override
            public boolean __equals(Object obj, boolean shallow) {
                return shallow ? __shallowEquals(obj) : equals(obj);
            }

            @Override
            public String toString() {
                return ImmutableObjects.toString(this);
            }
        }

        @GeneratedBy(
                type = Video.class
        )
        private static class DraftImpl implements Implementor, DraftSpi, VideoDraft {
            private DraftContext __ctx;

            private Impl __base;

            private Impl __modified;

            private boolean __resolving;

            private Video __resolved;

            DraftImpl(DraftContext ctx, Video base) {
                __ctx = ctx;
                if (base != null) {
                    __base = (Impl)base;
                }
                else {
                    __modified = new Impl();
                }
            }

            @Override
            public boolean __isLoaded(PropId prop) {
                return (__modified!= null ? __modified : __base).__isLoaded(prop);
            }

            @Override
            public boolean __isLoaded(String prop) {
                return (__modified!= null ? __modified : __base).__isLoaded(prop);
            }

            @Override
            public boolean __isVisible(PropId prop) {
                return (__modified!= null ? __modified : __base).__isVisible(prop);
            }

            @Override
            public boolean __isVisible(String prop) {
                return (__modified!= null ? __modified : __base).__isVisible(prop);
            }

            @Override
            public int hashCode() {
                return (__modified!= null ? __modified : __base).hashCode();
            }

            @Override
            public int __hashCode(boolean shallow) {
                return (__modified!= null ? __modified : __base).__hashCode(shallow);
            }

            @Override
            public boolean equals(Object obj) {
                return (__modified!= null ? __modified : __base).equals(obj);
            }

            @Override
            public boolean __equals(Object obj, boolean shallow) {
                return (__modified!= null ? __modified : __base).__equals(obj, shallow);
            }

            @Override
            public String toString() {
                return ImmutableObjects.toString(this);
            }

            @Override
            @JsonIgnore
            public long id() {
                return (__modified!= null ? __modified : __base).id();
            }

            @Override
            public VideoDraft setId(long id) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                Impl __tmpModified = __modified();
                __tmpModified.__idValue = id;
                __tmpModified.__idLoaded = true;
                return this;
            }

            @Override
            @JsonIgnore
            public Date createTime() {
                return (__modified!= null ? __modified : __base).createTime();
            }

            @Override
            public VideoDraft setCreateTime(Date createTime) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (createTime == null) {
                    throw new IllegalArgumentException(
                        "'createTime' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__createTimeValue = createTime;
                return this;
            }

            @Override
            @JsonIgnore
            @Nullable
            public Date modifyTime() {
                return (__modified!= null ? __modified : __base).modifyTime();
            }

            @Override
            public VideoDraft setModifyTime(Date modifyTime) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                Impl __tmpModified = __modified();
                __tmpModified.__modifyTimeValue = modifyTime;
                __tmpModified.__modifyTimeLoaded = true;
                return this;
            }

            @Override
            @JsonIgnore
            public String betweenTime() {
                return (__modified!= null ? __modified : __base).betweenTime();
            }

            @Override
            @JsonIgnore
            public String description() {
                return (__modified!= null ? __modified : __base).description();
            }

            @Override
            public VideoDraft setDescription(String description) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (description == null) {
                    throw new IllegalArgumentException(
                        "'description' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__descriptionValue = description;
                return this;
            }

            @Override
            @JsonIgnore
            public String status() {
                return (__modified!= null ? __modified : __base).status();
            }

            @Override
            public VideoDraft setStatus(String status) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (status == null) {
                    throw new IllegalArgumentException(
                        "'status' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__statusValue = status;
                return this;
            }

            @Override
            @JsonIgnore
            public TypeDraft type() {
                return __ctx.toDraftObject((__modified!= null ? __modified : __base).type());
            }

            @Override
            public TypeDraft type(boolean autoCreate) {
                if (autoCreate && (!__isLoaded(PropId.byIndex(SLOT_TYPE)))) {
                    setType(TypeDraft.$.produce(null, null));
                }
                return __ctx.toDraftObject((__modified!= null ? __modified : __base).type());
            }

            @Override
            public VideoDraft setType(Type type) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (type == null) {
                    throw new IllegalArgumentException(
                        "'type' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__typeValue = type;
                return this;
            }

            @Override
            public long typeId() {
                return type().id();
            }

            @OldChain
            @Override
            public VideoDraft setTypeId(long typeId) {
                type(true).setId(Objects.requireNonNull(typeId, "\"type\" cannot be null"));
                return this;
            }

            @Override
            public VideoDraft applyType(DraftConsumer<TypeDraft> block) {
                applyType(null, block);
                return this;
            }

            @Override
            public VideoDraft applyType(Type base, DraftConsumer<TypeDraft> block) {
                setType(TypeDraft.$.produce(base, block));
                return this;
            }

            @Override
            @JsonIgnore
            public UserDraft user() {
                return __ctx.toDraftObject((__modified!= null ? __modified : __base).user());
            }

            @Override
            public UserDraft user(boolean autoCreate) {
                if (autoCreate && (!__isLoaded(PropId.byIndex(SLOT_USER)))) {
                    setUser(UserDraft.$.produce(null, null));
                }
                return __ctx.toDraftObject((__modified!= null ? __modified : __base).user());
            }

            @Override
            public VideoDraft setUser(User user) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (user == null) {
                    throw new IllegalArgumentException(
                        "'user' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__userValue = user;
                return this;
            }

            @Override
            public long userId() {
                return user().id();
            }

            @OldChain
            @Override
            public VideoDraft setUserId(long userId) {
                user(true).setId(Objects.requireNonNull(userId, "\"user\" cannot be null"));
                return this;
            }

            @Override
            public VideoDraft applyUser(DraftConsumer<UserDraft> block) {
                applyUser(null, block);
                return this;
            }

            @Override
            public VideoDraft applyUser(User base, DraftConsumer<UserDraft> block) {
                setUser(UserDraft.$.produce(base, block));
                return this;
            }

            @Override
            @JsonIgnore
            public List<VideoLike> likes() {
                return __ctx.toDraftList((__modified!= null ? __modified : __base).likes(), VideoLike.class, true);
            }

            @Override
            public List<VideoLikeDraft> likes(boolean autoCreate) {
                if (autoCreate && (!__isLoaded(PropId.byIndex(SLOT_LIKES)))) {
                    setLikes(new ArrayList<>());
                }
                return __ctx.toDraftList((__modified!= null ? __modified : __base).likes(), VideoLike.class, true);
            }

            @Override
            public VideoDraft setLikes(List<VideoLike> likes) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (likes == null) {
                    throw new IllegalArgumentException(
                        "'likes' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__likesValue = NonSharedList.of(__tmpModified.__likesValue, likes);
                return this;
            }

            @Override
            public VideoDraft addIntoLikes(DraftConsumer<VideoLikeDraft> block) {
                addIntoLikes(null, block);
                return this;
            }

            @Override
            public VideoDraft addIntoLikes(VideoLike base, DraftConsumer<VideoLikeDraft> block) {
                likes(true).add((VideoLikeDraft)VideoLikeDraft.$.produce(base, block));
                return this;
            }

            @Override
            @JsonIgnore
            public List<Comment> comments() {
                return __ctx.toDraftList((__modified!= null ? __modified : __base).comments(), Comment.class, true);
            }

            @Override
            public List<CommentDraft> comments(boolean autoCreate) {
                if (autoCreate && (!__isLoaded(PropId.byIndex(SLOT_COMMENTS)))) {
                    setComments(new ArrayList<>());
                }
                return __ctx.toDraftList((__modified!= null ? __modified : __base).comments(), Comment.class, true);
            }

            @Override
            public VideoDraft setComments(List<Comment> comments) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (comments == null) {
                    throw new IllegalArgumentException(
                        "'comments' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__commentsValue = NonSharedList.of(__tmpModified.__commentsValue, comments);
                return this;
            }

            @Override
            public VideoDraft addIntoComments(DraftConsumer<CommentDraft> block) {
                addIntoComments(null, block);
                return this;
            }

            @Override
            public VideoDraft addIntoComments(Comment base, DraftConsumer<CommentDraft> block) {
                comments(true).add((CommentDraft)CommentDraft.$.produce(base, block));
                return this;
            }

            @Override
            @JsonIgnore
            public List<Collection> collections() {
                return __ctx.toDraftList((__modified!= null ? __modified : __base).collections(), Collection.class, true);
            }

            @Override
            public List<CollectionDraft> collections(boolean autoCreate) {
                if (autoCreate && (!__isLoaded(PropId.byIndex(SLOT_COLLECTIONS)))) {
                    setCollections(new ArrayList<>());
                }
                return __ctx.toDraftList((__modified!= null ? __modified : __base).collections(), Collection.class, true);
            }

            @Override
            public VideoDraft setCollections(List<Collection> collections) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (collections == null) {
                    throw new IllegalArgumentException(
                        "'collections' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__collectionsValue = NonSharedList.of(__tmpModified.__collectionsValue, collections);
                return this;
            }

            @Override
            public VideoDraft addIntoCollections(DraftConsumer<CollectionDraft> block) {
                addIntoCollections(null, block);
                return this;
            }

            @Override
            public VideoDraft addIntoCollections(Collection base,
                    DraftConsumer<CollectionDraft> block) {
                collections(true).add((CollectionDraft)CollectionDraft.$.produce(base, block));
                return this;
            }

            @Override
            @JsonIgnore
            public String source() {
                return (__modified!= null ? __modified : __base).source();
            }

            @Override
            @JsonIgnore
            public String poster() {
                return (__modified!= null ? __modified : __base).poster();
            }

            @Override
            @JsonIgnore
            public long likesSum() {
                return (__modified!= null ? __modified : __base).likesSum();
            }

            @Override
            @JsonIgnore
            public long commentsSum() {
                return (__modified!= null ? __modified : __base).commentsSum();
            }

            @Override
            @JsonIgnore
            public long collectionsSum() {
                return (__modified!= null ? __modified : __base).collectionsSum();
            }

            @Override
            @JsonIgnore
            public boolean userIsAdore() {
                return (__modified!= null ? __modified : __base).userIsAdore();
            }

            @Override
            @JsonIgnore
            public boolean userIsLike() {
                return (__modified!= null ? __modified : __base).userIsLike();
            }

            @Override
            @JsonIgnore
            public boolean userIsCollect() {
                return (__modified!= null ? __modified : __base).userIsCollect();
            }

            @SuppressWarnings("unchecked")
            @Override
            public void __set(PropId prop, Object value) {
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		__set(prop.asName(), value);
                    return;
                    case SLOT_CREATE_TIME:
                    		setCreateTime((Date)value);break;
                    case SLOT_MODIFY_TIME:
                    		setModifyTime((Date)value);break;
                    case SLOT_BETWEEN_TIME:
                    		break;
                    case SLOT_ID:
                    		if (value == null) throw new IllegalArgumentException("'id' cannot be null, if you want to set null, please use any annotation whose simple name is \"Nullable\" to decorate the property");
                            setId((Long)value);
                            break;
                    case SLOT_DESCRIPTION:
                    		setDescription((String)value);break;
                    case SLOT_STATUS:
                    		setStatus((String)value);break;
                    case SLOT_TYPE:
                    		setType((Type)value);break;
                    case SLOT_USER:
                    		setUser((User)value);break;
                    case SLOT_LIKES:
                    		setLikes((List<VideoLike>)value);break;
                    case SLOT_COMMENTS:
                    		setComments((List<Comment>)value);break;
                    case SLOT_COLLECTIONS:
                    		setCollections((List<Collection>)value);break;
                    case SLOT_SOURCE:
                    		break;
                    case SLOT_POSTER:
                    		break;
                    case SLOT_LIKES_SUM:
                    		break;
                    case SLOT_COMMENTS_SUM:
                    		break;
                    case SLOT_COLLECTIONS_SUM:
                    		break;
                    case SLOT_USER_IS_ADORE:
                    		break;
                    case SLOT_USER_IS_LIKE:
                    		break;
                    case SLOT_USER_IS_COLLECT:
                    		break;
                    default: throw new IllegalArgumentException("Illegal property id for \"com.celeste.entity.Video\": \"" + prop + "\"");
                }
            }

            @SuppressWarnings("unchecked")
            @Override
            public void __set(String prop, Object value) {
                switch (prop) {
                    case "createTime":
                    		setCreateTime((Date)value);break;
                    case "modifyTime":
                    		setModifyTime((Date)value);break;
                    case "betweenTime":
                    		break;
                    case "id":
                    		if (value == null) throw new IllegalArgumentException("'id' cannot be null, if you want to set null, please use any annotation whose simple name is \"Nullable\" to decorate the property");
                            setId((Long)value);
                            break;
                    case "description":
                    		setDescription((String)value);break;
                    case "status":
                    		setStatus((String)value);break;
                    case "type":
                    		setType((Type)value);break;
                    case "user":
                    		setUser((User)value);break;
                    case "likes":
                    		setLikes((List<VideoLike>)value);break;
                    case "comments":
                    		setComments((List<Comment>)value);break;
                    case "collections":
                    		setCollections((List<Collection>)value);break;
                    case "source":
                    		break;
                    case "poster":
                    		break;
                    case "likesSum":
                    		break;
                    case "commentsSum":
                    		break;
                    case "collectionsSum":
                    		break;
                    case "userIsAdore":
                    		break;
                    case "userIsLike":
                    		break;
                    case "userIsCollect":
                    		break;
                    default: throw new IllegalArgumentException("Illegal property name for \"com.celeste.entity.Video\": \"" + prop + "\"");
                }
            }

            @Override
            public void __show(PropId prop, boolean visible) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                Visibility __visibility = (__modified!= null ? __modified : __base).__visibility;
                if (__visibility == null) {
                    if (visible) {
                        return;
                    }
                    __modified().__visibility = __visibility = Visibility.of(19);
                }
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		__show(prop.asName(), visible);
                    return;
                    case SLOT_CREATE_TIME:
                    		__visibility.show(SLOT_CREATE_TIME, visible);break;
                    case SLOT_MODIFY_TIME:
                    		__visibility.show(SLOT_MODIFY_TIME, visible);break;
                    case SLOT_BETWEEN_TIME:
                    		__visibility.show(SLOT_BETWEEN_TIME, visible);break;
                    case SLOT_ID:
                    		__visibility.show(SLOT_ID, visible);break;
                    case SLOT_DESCRIPTION:
                    		__visibility.show(SLOT_DESCRIPTION, visible);break;
                    case SLOT_STATUS:
                    		__visibility.show(SLOT_STATUS, visible);break;
                    case SLOT_TYPE:
                    		__visibility.show(SLOT_TYPE, visible);break;
                    case SLOT_USER:
                    		__visibility.show(SLOT_USER, visible);break;
                    case SLOT_LIKES:
                    		__visibility.show(SLOT_LIKES, visible);break;
                    case SLOT_COMMENTS:
                    		__visibility.show(SLOT_COMMENTS, visible);break;
                    case SLOT_COLLECTIONS:
                    		__visibility.show(SLOT_COLLECTIONS, visible);break;
                    case SLOT_SOURCE:
                    		__visibility.show(SLOT_SOURCE, visible);break;
                    case SLOT_POSTER:
                    		__visibility.show(SLOT_POSTER, visible);break;
                    case SLOT_LIKES_SUM:
                    		__visibility.show(SLOT_LIKES_SUM, visible);break;
                    case SLOT_COMMENTS_SUM:
                    		__visibility.show(SLOT_COMMENTS_SUM, visible);break;
                    case SLOT_COLLECTIONS_SUM:
                    		__visibility.show(SLOT_COLLECTIONS_SUM, visible);break;
                    case SLOT_USER_IS_ADORE:
                    		__visibility.show(SLOT_USER_IS_ADORE, visible);break;
                    case SLOT_USER_IS_LIKE:
                    		__visibility.show(SLOT_USER_IS_LIKE, visible);break;
                    case SLOT_USER_IS_COLLECT:
                    		__visibility.show(SLOT_USER_IS_COLLECT, visible);break;
                    default: throw new IllegalArgumentException(
                                "Illegal property id for \"com.celeste.entity.Video\": \"" + 
                                prop + 
                                "\",it does not exists"
                            );
                }
            }

            @Override
            public void __show(String prop, boolean visible) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                Visibility __visibility = (__modified!= null ? __modified : __base).__visibility;
                if (__visibility == null) {
                    if (visible) {
                        return;
                    }
                    __modified().__visibility = __visibility = Visibility.of(19);
                }
                switch (prop) {
                    case "createTime":
                    		__visibility.show(SLOT_CREATE_TIME, visible);break;
                    case "modifyTime":
                    		__visibility.show(SLOT_MODIFY_TIME, visible);break;
                    case "betweenTime":
                    		__visibility.show(SLOT_BETWEEN_TIME, visible);break;
                    case "id":
                    		__visibility.show(SLOT_ID, visible);break;
                    case "description":
                    		__visibility.show(SLOT_DESCRIPTION, visible);break;
                    case "status":
                    		__visibility.show(SLOT_STATUS, visible);break;
                    case "type":
                    		__visibility.show(SLOT_TYPE, visible);break;
                    case "user":
                    		__visibility.show(SLOT_USER, visible);break;
                    case "likes":
                    		__visibility.show(SLOT_LIKES, visible);break;
                    case "comments":
                    		__visibility.show(SLOT_COMMENTS, visible);break;
                    case "collections":
                    		__visibility.show(SLOT_COLLECTIONS, visible);break;
                    case "source":
                    		__visibility.show(SLOT_SOURCE, visible);break;
                    case "poster":
                    		__visibility.show(SLOT_POSTER, visible);break;
                    case "likesSum":
                    		__visibility.show(SLOT_LIKES_SUM, visible);break;
                    case "commentsSum":
                    		__visibility.show(SLOT_COMMENTS_SUM, visible);break;
                    case "collectionsSum":
                    		__visibility.show(SLOT_COLLECTIONS_SUM, visible);break;
                    case "userIsAdore":
                    		__visibility.show(SLOT_USER_IS_ADORE, visible);break;
                    case "userIsLike":
                    		__visibility.show(SLOT_USER_IS_LIKE, visible);break;
                    case "userIsCollect":
                    		__visibility.show(SLOT_USER_IS_COLLECT, visible);break;
                    default: throw new IllegalArgumentException(
                                "Illegal property name for \"com.celeste.entity.Video\": \"" + 
                                prop + 
                                "\",it does not exists"
                            );
                }
            }

            @Override
            public void __unload(PropId prop) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		__unload(prop.asName());
                    return;
                    case SLOT_CREATE_TIME:
                    		__modified().__createTimeValue = null;break;
                    case SLOT_MODIFY_TIME:
                    		__modified().__modifyTimeLoaded = false;break;
                    case SLOT_BETWEEN_TIME:
                    		break;
                    case SLOT_ID:
                    		__modified().__idLoaded = false;break;
                    case SLOT_DESCRIPTION:
                    		__modified().__descriptionValue = null;break;
                    case SLOT_STATUS:
                    		__modified().__statusValue = null;break;
                    case SLOT_TYPE:
                    		__modified().__typeValue = null;break;
                    case SLOT_USER:
                    		__modified().__userValue = null;break;
                    case SLOT_LIKES:
                    		__modified().__likesValue = null;break;
                    case SLOT_COMMENTS:
                    		__modified().__commentsValue = null;break;
                    case SLOT_COLLECTIONS:
                    		__modified().__collectionsValue = null;break;
                    case SLOT_SOURCE:
                    		break;
                    case SLOT_POSTER:
                    		break;
                    case SLOT_LIKES_SUM:
                    		break;
                    case SLOT_COMMENTS_SUM:
                    		break;
                    case SLOT_COLLECTIONS_SUM:
                    		break;
                    case SLOT_USER_IS_ADORE:
                    		break;
                    case SLOT_USER_IS_LIKE:
                    		break;
                    case SLOT_USER_IS_COLLECT:
                    		break;
                    default: throw new IllegalArgumentException("Illegal property id for \"com.celeste.entity.Video\": \"" + prop + "\", it does not exist or its loaded state is not controllable");
                }
            }

            @Override
            public void __unload(String prop) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                switch (prop) {
                    case "createTime":
                    		__modified().__createTimeValue = null;break;
                    case "modifyTime":
                    		__modified().__modifyTimeLoaded = false;break;
                    case "betweenTime":
                    		break;
                    case "id":
                    		__modified().__idLoaded = false;break;
                    case "description":
                    		__modified().__descriptionValue = null;break;
                    case "status":
                    		__modified().__statusValue = null;break;
                    case "type":
                    		__modified().__typeValue = null;break;
                    case "user":
                    		__modified().__userValue = null;break;
                    case "likes":
                    		__modified().__likesValue = null;break;
                    case "comments":
                    		__modified().__commentsValue = null;break;
                    case "collections":
                    		__modified().__collectionsValue = null;break;
                    case "source":
                    		break;
                    case "poster":
                    		break;
                    case "likesSum":
                    		break;
                    case "commentsSum":
                    		break;
                    case "collectionsSum":
                    		break;
                    case "userIsAdore":
                    		break;
                    case "userIsLike":
                    		break;
                    case "userIsCollect":
                    		break;
                    default: throw new IllegalArgumentException("Illegal property name for \"com.celeste.entity.Video\": \"" + prop + "\", it does not exist or its loaded state is not controllable");
                }
            }

            @Override
            public DraftContext __draftContext() {
                return __ctx;
            }

            @Override
            public Object __resolve() {
                if (__resolved != null) {
                    return __resolved;
                }
                if (__resolving) {
                    throw new CircularReferenceException();
                }
                __resolving = true;
                try {
                    Implementor base = __base;
                    Impl __tmpModified = __modified;
                    if (__tmpModified == null) {
                        if (base.__isLoaded(PropId.byIndex(SLOT_TYPE))) {
                            Type oldValue = base.type();
                            Type newValue = __ctx.resolveObject(oldValue);
                            if (oldValue != newValue) {
                                setType(newValue);
                            }
                        }
                        if (base.__isLoaded(PropId.byIndex(SLOT_USER))) {
                            User oldValue = base.user();
                            User newValue = __ctx.resolveObject(oldValue);
                            if (oldValue != newValue) {
                                setUser(newValue);
                            }
                        }
                        if (base.__isLoaded(PropId.byIndex(SLOT_LIKES))) {
                            List<VideoLike> oldValue = base.likes();
                            List<VideoLike> newValue = __ctx.resolveList(oldValue);
                            if (oldValue != newValue) {
                                setLikes(newValue);
                            }
                        }
                        if (base.__isLoaded(PropId.byIndex(SLOT_COMMENTS))) {
                            List<Comment> oldValue = base.comments();
                            List<Comment> newValue = __ctx.resolveList(oldValue);
                            if (oldValue != newValue) {
                                setComments(newValue);
                            }
                        }
                        if (base.__isLoaded(PropId.byIndex(SLOT_COLLECTIONS))) {
                            List<Collection> oldValue = base.collections();
                            List<Collection> newValue = __ctx.resolveList(oldValue);
                            if (oldValue != newValue) {
                                setCollections(newValue);
                            }
                        }
                        __tmpModified = __modified;
                    }
                    else {
                        __tmpModified.__typeValue = __ctx.resolveObject(__tmpModified.__typeValue);
                        __tmpModified.__userValue = __ctx.resolveObject(__tmpModified.__userValue);
                        __tmpModified.__likesValue = NonSharedList.of(__tmpModified.__likesValue, __ctx.resolveList(__tmpModified.__likesValue));
                        __tmpModified.__commentsValue = NonSharedList.of(__tmpModified.__commentsValue, __ctx.resolveList(__tmpModified.__commentsValue));
                        __tmpModified.__collectionsValue = NonSharedList.of(__tmpModified.__collectionsValue, __ctx.resolveList(__tmpModified.__collectionsValue));
                    }
                    if (__base != null && __tmpModified == null) {
                        this.__resolved = base;
                        return base;
                    }
                    this.__resolved = __tmpModified;
                    return __tmpModified;
                }
                finally {
                    __resolving = false;
                }
            }

            @Override
            public boolean __isResolved() {
                return __resolved != null;
            }

            Impl __modified() {
                Impl __tmpModified = __modified;
                if (__tmpModified == null) {
                    __tmpModified = __base.clone();
                    __modified = __tmpModified;
                }
                return __tmpModified;
            }
        }
    }

    @GeneratedBy(
            type = Video.class
    )
    class Builder {
        private final Producer.DraftImpl __draft;

        public Builder() {
            __draft = new Producer.DraftImpl(null, null);
            __draft.__show(PropId.byIndex(Producer.SLOT_ID), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_CREATE_TIME), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_BETWEEN_TIME), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_USER), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_LIKES), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_COMMENTS), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_COLLECTIONS), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_SOURCE), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_POSTER), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_LIKES_SUM), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_COMMENTS_SUM), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_COLLECTIONS_SUM), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_USER_IS_ADORE), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_USER_IS_LIKE), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_USER_IS_COLLECT), false);
        }

        public Builder id(Long id) {
            if (id != null) {
                __draft.setId(id);
                __draft.__show(PropId.byIndex(Producer.SLOT_ID), true);
            }
            return this;
        }

        public Builder createTime(Date createTime) {
            if (createTime != null) {
                __draft.setCreateTime(createTime);
                __draft.__show(PropId.byIndex(Producer.SLOT_CREATE_TIME), true);
            }
            return this;
        }

        @Nullable
        public Builder modifyTime(Date modifyTime) {
            __draft.setModifyTime(modifyTime);
            return this;
        }

        public Builder description(String description) {
            if (description != null) {
                __draft.setDescription(description);
            }
            return this;
        }

        public Builder status(String status) {
            if (status != null) {
                __draft.setStatus(status);
            }
            return this;
        }

        public Builder type(Type type) {
            if (type != null) {
                __draft.setType(type);
            }
            return this;
        }

        public Builder user(User user) {
            if (user != null) {
                __draft.setUser(user);
                __draft.__show(PropId.byIndex(Producer.SLOT_USER), true);
            }
            return this;
        }

        public Builder likes(List<VideoLike> likes) {
            if (likes != null) {
                __draft.setLikes(likes);
                __draft.__show(PropId.byIndex(Producer.SLOT_LIKES), true);
            }
            return this;
        }

        public Builder comments(List<Comment> comments) {
            if (comments != null) {
                __draft.setComments(comments);
                __draft.__show(PropId.byIndex(Producer.SLOT_COMMENTS), true);
            }
            return this;
        }

        public Builder collections(List<Collection> collections) {
            if (collections != null) {
                __draft.setCollections(collections);
                __draft.__show(PropId.byIndex(Producer.SLOT_COLLECTIONS), true);
            }
            return this;
        }

        public Video build() {
            return (Video)__draft.__modified();
        }
    }
}
