package com.celeste.dto;

import java.util.Date;

/**
 * 管理员查看视频信息DTO
 */
public class AdminVideoDto {
    private Long id;
    private String description;
    private String status;
    private Date createTime;
    private Date modifyTime;
    private String username; // 上传者用户名
    private Long userId; // 上传者ID
    private String typeName; // 视频类型名称
    private Long typeId; // 视频类型ID
    private long likesSum;
    private long commentsSum;
    private long collectionsSum;
    private String source; // 视频源地址
    private String poster; // 视频封面地址

    // 构造函数
    public AdminVideoDto() {}

    public AdminVideoDto(Long id, String description, String status, Date createTime,
                        Date modifyTime, String username, Long userId, String typeName,
                        Long typeId, long likesSum, long commentsSum, long collectionsSum,
                        String source, String poster) {
        this.id = id;
        this.description = description;
        this.status = status;
        this.createTime = createTime;
        this.modifyTime = modifyTime;
        this.username = username;
        this.userId = userId;
        this.typeName = typeName;
        this.typeId = typeId;
        this.likesSum = likesSum;
        this.commentsSum = commentsSum;
        this.collectionsSum = collectionsSum;
        this.source = source;
        this.poster = poster;
    }

    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }

    public Date getCreateTime() { return createTime; }
    public void setCreateTime(Date createTime) { this.createTime = createTime; }

    public Date getModifyTime() { return modifyTime; }
    public void setModifyTime(Date modifyTime) { this.modifyTime = modifyTime; }

    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }

    public Long getUserId() { return userId; }
    public void setUserId(Long userId) { this.userId = userId; }

    public String getTypeName() { return typeName; }
    public void setTypeName(String typeName) { this.typeName = typeName; }

    public Long getTypeId() { return typeId; }
    public void setTypeId(Long typeId) { this.typeId = typeId; }

    public long getLikesSum() { return likesSum; }
    public void setLikesSum(long likesSum) { this.likesSum = likesSum; }

    public long getCommentsSum() { return commentsSum; }
    public void setCommentsSum(long commentsSum) { this.commentsSum = commentsSum; }

    public long getCollectionsSum() { return collectionsSum; }
    public void setCollectionsSum(long collectionsSum) { this.collectionsSum = collectionsSum; }

    public String getSource() { return source; }
    public void setSource(String source) { this.source = source; }

    public String getPoster() { return poster; }
    public void setPoster(String poster) { this.poster = poster; }
}
