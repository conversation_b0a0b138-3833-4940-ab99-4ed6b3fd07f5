package com.celeste.dto;

import java.util.Date;
import java.util.List;

/**
 * 管理员查看用户信息DTO
 */
public class AdminUserDto {
    private Long id;
    private String username;
    private String email;
    private String description;
    private String gender;
    private String home;
    private String status;
    private Date createTime;
    private Date updateTime;
    private List<String> roleList;
    private long followersSum;
    private long adoresSum;
    private long videosCount;

    // 构造函数
    public AdminUserDto() {}

    public AdminUserDto(Long id, String username, String email, String description, 
                       String gender, String home, String status, Date createTime, 
                       Date updateTime, List<String> roleList, long followersSum, 
                       long adoresSum, long videosCount) {
        this.id = id;
        this.username = username;
        this.email = email;
        this.description = description;
        this.gender = gender;
        this.home = home;
        this.status = status;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.roleList = roleList;
        this.followersSum = followersSum;
        this.adoresSum = adoresSum;
        this.videosCount = videosCount;
    }

    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }

    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public String getGender() { return gender; }
    public void setGender(String gender) { this.gender = gender; }

    public String getHome() { return home; }
    public void setHome(String home) { this.home = home; }

    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }

    public Date getCreateTime() { return createTime; }
    public void setCreateTime(Date createTime) { this.createTime = createTime; }

    public Date getUpdateTime() { return updateTime; }
    public void setUpdateTime(Date updateTime) { this.updateTime = updateTime; }

    public List<String> getRoleList() { return roleList; }
    public void setRoleList(List<String> roleList) { this.roleList = roleList; }

    public long getFollowersSum() { return followersSum; }
    public void setFollowersSum(long followersSum) { this.followersSum = followersSum; }

    public long getAdoresSum() { return adoresSum; }
    public void setAdoresSum(long adoresSum) { this.adoresSum = adoresSum; }

    public long getVideosCount() { return videosCount; }
    public void setVideosCount(long videosCount) { this.videosCount = videosCount; }
}
