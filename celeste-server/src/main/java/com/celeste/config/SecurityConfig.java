package com.celeste.config;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.celeste.filter.JwtFilter;
import com.celeste.model.Result;
import com.celeste.model.SecurityUser;
import com.celeste.utils.JwtUtil;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Collections;

@Configuration
@EnableWebSecurity
@EnableMethodSecurity
public class SecurityConfig {

    @Resource
    private JwtFilter jwtFilter;

    @Bean
    public BCryptPasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        return http
                .authorizeHttpRequests(config -> config
                        // 明确允许这些公开接口
                        .requestMatchers("/api/user/askCode", "/api/user/register",
                                       "/api/user/resetPasswordConfirm", "/api/user/resetPassword",
                                       "/api/user/getAvatar/**", "/api/user/getUserInfo/**",
                                       "/api/video/recommend", "/api/video/recommendType",
                                       "/api/video/searchVideo", "/api/censor/**").permitAll()
                        // 其他请求也允许访问（保持原有逻辑）
                        .anyRequest().permitAll())
                .formLogin(config -> config
                        .loginProcessingUrl("/api/user/login")
                        .usernameParameter("usernameOrEmail")
                        .successHandler((request, response, authentication) -> {
                            SecurityUser securityUser = (SecurityUser) authentication.getPrincipal();
                            JSONObject object = JSONUtil.createObj()
                                    .set("id", securityUser.user().id())
                                    .set("jwt", JwtUtil.create(securityUser))
                                    .set("username", securityUser.getUsername());
                            response.setCharacterEncoding("utf-8");
                            response.setContentType("application/json");
                            response.getWriter().write(Result.success(object).toJson());
                        })
                        .failureHandler((request, response, e) -> {
                            response.setCharacterEncoding("utf-8");
                            response.setContentType("application/json");
                            response.getWriter().write(Result.failure("账号或密码错误").toJson());
                        })
                )
                .csrf(AbstractHttpConfigurer::disable)
                .cors(config -> config.configurationSource(configurationSource()))
                .sessionManagement(config -> config.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                .addFilterBefore(jwtFilter, UsernamePasswordAuthenticationFilter.class)
                .build();
    }

    private CorsConfigurationSource configurationSource() {
        CorsConfiguration corsConfiguration = new CorsConfiguration();
        corsConfiguration.setAllowedOrigins(Collections.singletonList("*"));
        corsConfiguration.setAllowedMethods(Collections.singletonList("*"));
        corsConfiguration.setAllowedHeaders(Collections.singletonList("*"));
        UrlBasedCorsConfigurationSource urlBasedCorsConfigurationSource = new UrlBasedCorsConfigurationSource();
        urlBasedCorsConfigurationSource.registerCorsConfiguration("/**", corsConfiguration);
        return urlBasedCorsConfigurationSource;
    }

}
