package com.celeste.controller;

import com.celeste.utils.CensorUtil;
import com.celeste.utils.OssUtil;
import jakarta.validation.constraints.NotNull;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/api/censor")
public class CensorController {

    @PostMapping("/image")
    public boolean imageCensor(@NotNull MultipartFile image) {
        OssUtil.uploadFile(image, "image.jpg");
        String imgUrl = OssUtil.getUrl("image.jpg");
        return CensorUtil.imageCensor(imgUrl);
    }

    @PostMapping("/video")
    public boolean videoCensor(@NotNull MultipartFile video) {
        OssUtil.uploadFile(video, "video.mp4");
        String videoUrl = OssUtil.getUrl("video.mp4");
        return CensorUtil.videoCensor(videoUrl);
    }
}
