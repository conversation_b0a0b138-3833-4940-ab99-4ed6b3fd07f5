package com.celeste.controller;

import com.celeste.dto.AdminUserDto;
import com.celeste.dto.AdminVideoDto;
import com.celeste.service.AdminService;
import org.babyfish.jimmer.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 管理员控制器
 * 提供管理员操作的API接口
 */
@RestController
@RequestMapping("/api/admin")
@PreAuthorize("hasRole('ADMIN')")
public class AdminController {

    @Resource
    private AdminService adminService;

    /**
     * 获取所有用户信息（分页）
     * 
     * @param pageable 分页参数
     * @param status 用户状态过滤（可选）
     * @param keyword 关键词搜索（可选）
     * @return 用户信息分页结果
     */
    @GetMapping("/users")
    public Page<AdminUserDto> getAllUsers(
            @PageableDefault(size = 20) Pageable pageable,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String keyword) {
        return adminService.getAllUsers(pageable, status, keyword);
    }

    /**
     * 获取所有视频信息（分页）
     * 
     * @param pageable 分页参数
     * @param status 视频状态过滤（可选）
     * @param keyword 关键词搜索（可选）
     * @return 视频信息分页结果
     */
    @GetMapping("/videos")
    public Page<AdminVideoDto> getAllVideos(
            @PageableDefault(size = 20) Pageable pageable,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String keyword) {
        return adminService.getAllVideos(pageable, status, keyword);
    }

    /**
     * 禁用用户
     * 
     * @param userId 用户ID
     */
    @PostMapping("/users/{userId}/disable")
    public void disableUser(@PathVariable Long userId) {
        adminService.disableUser(userId);
    }

    /**
     * 启用用户
     * 
     * @param userId 用户ID
     */
    @PostMapping("/users/{userId}/enable")
    public void enableUser(@PathVariable Long userId) {
        adminService.enableUser(userId);
    }

    /**
     * 删除用户（软删除）
     * 
     * @param userId 用户ID
     */
    @DeleteMapping("/users/{userId}")
    public void deleteUser(@PathVariable Long userId) {
        adminService.deleteUser(userId);
    }

    /**
     * 下架视频
     * 
     * @param videoId 视频ID
     */
    @PostMapping("/videos/{videoId}/hide")
    public void hideVideo(@PathVariable Long videoId) {
        adminService.hideVideo(videoId);
    }

    /**
     * 上架视频
     * 
     * @param videoId 视频ID
     */
    @PostMapping("/videos/{videoId}/show")
    public void showVideo(@PathVariable Long videoId) {
        adminService.showVideo(videoId);
    }

    /**
     * 删除视频（软删除）
     * 
     * @param videoId 视频ID
     */
    @DeleteMapping("/videos/{videoId}")
    public void deleteVideo(@PathVariable Long videoId) {
        adminService.deleteVideo(videoId);
    }

    /**
     * 获取系统统计信息
     * 
     * @return 系统统计数据
     */
    @GetMapping("/stats")
    public AdminService.AdminStatsDto getSystemStats() {
        return adminService.getSystemStats();
    }

    /**
     * 批量操作用户
     * 
     * @param userIds 用户ID列表
     * @param action 操作类型：disable, enable, delete
     */
    @PostMapping("/users/batch")
    public void batchOperateUsers(@RequestBody BatchOperationRequest request) {
        switch (request.getAction().toLowerCase()) {
            case "disable":
                request.getIds().forEach(adminService::disableUser);
                break;
            case "enable":
                request.getIds().forEach(adminService::enableUser);
                break;
            case "delete":
                request.getIds().forEach(adminService::deleteUser);
                break;
            default:
                throw new IllegalArgumentException("不支持的操作类型: " + request.getAction());
        }
    }

    /**
     * 批量操作视频
     * 
     * @param videoIds 视频ID列表
     * @param action 操作类型：hide, show, delete
     */
    @PostMapping("/videos/batch")
    public void batchOperateVideos(@RequestBody BatchOperationRequest request) {
        switch (request.getAction().toLowerCase()) {
            case "hide":
                request.getIds().forEach(adminService::hideVideo);
                break;
            case "show":
                request.getIds().forEach(adminService::showVideo);
                break;
            case "delete":
                request.getIds().forEach(adminService::deleteVideo);
                break;
            default:
                throw new IllegalArgumentException("不支持的操作类型: " + request.getAction());
        }
    }

    /**
     * 批量操作请求DTO
     */
    public static class BatchOperationRequest {
        private java.util.List<Long> ids;
        private String action;

        public java.util.List<Long> getIds() { return ids; }
        public void setIds(java.util.List<Long> ids) { this.ids = ids; }

        public String getAction() { return action; }
        public void setAction(String action) { this.action = action; }
    }
}
