package com.celeste.controller;

import com.celeste.entity.User;
import com.celeste.entity.UserFetcher;
import com.celeste.entity.dto.RegisterReq;
import com.celeste.entity.dto.ResetPasswordReq;
import com.celeste.entity.dto.UserReq;
import com.celeste.model.Group;
import com.celeste.model.SecurityUser;
import com.celeste.repository.UserRepository;
import com.celeste.service.UserService;
import com.celeste.utils.JwtUtil;
import com.celeste.utils.MinioUtil;
import com.feiniaojin.gracefulresponse.GracefulResponseException;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RestController
@RequestMapping("/api/user")
public class UserController {

    @Resource
    private UserService userService;

    @Resource
    private UserRepository userRepository;

    @GetMapping("/askCode")
    public void askCode(@NotBlank @Email String email, @NotBlank @Pattern(regexp = "(register|resetPassword|resetEmail)") String type, HttpServletRequest request) {
        System.out.println("收到验证码请求 - Email: " + email + ", Type: " + type + ", IP: " + request.getRemoteAddr());
        try {
            userService.askCode(email, type, request.getRemoteAddr());
            System.out.println("验证码发送成功 - Email: " + email);
        } catch (Exception e) {
            System.out.println("验证码发送失败 - Email: " + email + ", 错误: " + e.getMessage());
            throw e;
        }
    }

    @PostMapping("/register")
    public void register(@RequestBody @Validated RegisterReq req) {
        userService.register(req);
    }

    @PostMapping("/resetPasswordConfirm")
    public void resetPasswordConfirm(@RequestBody @Validated ResetPasswordReq req) {
        userService.resetPasswordConfirm(req);
    }

    @PostMapping("/resetPassword")
    public void resetPassword(@RequestBody @Validated(Group.Password.class) ResetPasswordReq req) {
        userService.resetPassword(req);
    }

    @GetMapping("/getAvatar/{username}")
    public void getAvatar(@PathVariable String username, HttpServletResponse response) {
        MinioUtil.getAvatar(username, response);
    }

    @GetMapping("/logout")
    @PreAuthorize("isAuthenticated()")
    public void logout(HttpServletRequest request) {
        JwtUtil.addBlacklist(request.getHeader("Authorization"));
    }

    @GetMapping("/getUserInfo/{userId}")
    public User getUserInfo(@PathVariable long userId) {
        return userRepository.findById(userId, UserFetcher.$.
                        username().email().description().gender().home().typeIds().adoresSum().followersSum().userIsAdore())
                .orElseThrow(() -> new GracefulResponseException("该用户不存在"));
    }

    @PostMapping("/uploadAvatar")
    @PreAuthorize("isAuthenticated()")
    public void uploadAvatar(@NotNull MultipartFile avatar, Authentication authentication) {
        SecurityUser securityUser = (SecurityUser) authentication.getPrincipal();
        String objectName = "avatar/" + securityUser.getUsername() + ".jpg";
        MinioUtil.uploadFile(avatar, objectName);
    }

    //关注用户
    @GetMapping("/adoreUser/{adoreId}")
    @PreAuthorize("isAuthenticated()")
    public void adoreUser(@PathVariable long adoreId, Authentication authentication) {
        SecurityUser securityUser = (SecurityUser) authentication.getPrincipal();
        if (userRepository.adoreUser(adoreId, securityUser.user().id()) == 0)
            throw new GracefulResponseException("用户关注失败,请联系管理员");
    }

    //取消关注用户
    @GetMapping("/unAdoreUser/{adoreId}")
    @PreAuthorize("isAuthenticated()")
    public void unAdoreUser(@PathVariable long adoreId, Authentication authentication) {
        SecurityUser securityUser = (SecurityUser) authentication.getPrincipal();
        if (userRepository.unAdoreUser(adoreId, securityUser.user().id()) == 0)
            throw new GracefulResponseException("用户取消关注失败,请联系管理员");
    }

    //移除粉丝
    @GetMapping("/unFollowerUser/{followerId}")
    @PreAuthorize("isAuthenticated()")
    public void unFollowerUser(@PathVariable long followerId, Authentication authentication) {
        SecurityUser securityUser = (SecurityUser) authentication.getPrincipal();
        if (userRepository.unAdoreUser(securityUser.user().id(), followerId) == 0)
            throw new GracefulResponseException("粉丝移除失败,请联系管理员");
    }

    @GetMapping("/getAdores/{userId}")
    public User getAdores(@PathVariable long userId) {
        return userRepository.listAdoresByUserId(userId);
    }

    @GetMapping("/getFollowers/{userId}")
    public User getFollowers(@PathVariable long userId) {
        return userRepository.listFollowersByUserId(userId);
    }

    @PostMapping("/updateUser")
    @PreAuthorize("isAuthenticated()")
    public void updateUser(@RequestBody UserReq req, Authentication authentication) {
        SecurityUser securityUser = (SecurityUser) authentication.getPrincipal();
        if (userRepository.updateUser(req, securityUser.user().id()) == 0)
            throw new GracefulResponseException("用户信息更新失败,请联系管理员");
    }

    /**
     * 搜索用户（用于开始新对话）
     */
    @GetMapping("/search")
    @PreAuthorize("isAuthenticated()")
    public List<User> searchUsers(@RequestParam(required = false) String keyword,
                                  @RequestParam(defaultValue = "0") int page,
                                  @RequestParam(defaultValue = "20") int size,
                                  Authentication authentication) {
        SecurityUser securityUser = (SecurityUser) authentication.getPrincipal();
        return userRepository.searchUsers(keyword, securityUser.user().id(), page, size);
    }

    /**
     * 获取所有用户（用于开始新对话）
     */
    @GetMapping("/all")
    @PreAuthorize("isAuthenticated()")
    public List<User> getAllUsers(@RequestParam(defaultValue = "0") int page,
                                  @RequestParam(defaultValue = "20") int size,
                                  Authentication authentication) {
        SecurityUser securityUser = (SecurityUser) authentication.getPrincipal();
        return userRepository.getAllUsersExceptCurrent(securityUser.user().id(), page, size);
    }

}

