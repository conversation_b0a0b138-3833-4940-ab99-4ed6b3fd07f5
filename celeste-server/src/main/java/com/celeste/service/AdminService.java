package com.celeste.service;

import com.celeste.dto.AdminUserDto;
import com.celeste.dto.AdminVideoDto;
import com.celeste.entity.*;
import com.celeste.enums.UserStatus;
import com.celeste.enums.VideoStatus;

import com.celeste.repository.UserRepository;
import com.celeste.repository.VideoRepository;
import com.feiniaojin.gracefulresponse.GracefulResponseException;
import jakarta.annotation.Resource;
import org.babyfish.jimmer.Page;
import org.babyfish.jimmer.sql.fetcher.Fetcher;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 管理员服务类
 */
@Service
@Transactional
public class AdminService {

    @Resource
    private UserRepository userRepository;

    @Resource
    private VideoRepository videoRepository;

    /**
     * 获取所有用户信息（分页）
     */
    public Page<AdminUserDto> getAllUsers(Pageable pageable, String status, String keyword) {
        UserTable userTable = UserTable.$;

        return userRepository.sql()
                .createQuery(userTable)
                .whereIf(status != null && !status.isEmpty(), userTable.status().eq(status))
                .whereIf(keyword != null && !keyword.isEmpty(),
                    userTable.username().like("%" + keyword + "%")
                    .or(userTable.email().like("%" + keyword + "%")))
                .orderBy(userTable.createTime().desc())
                .select(userTable.fetch(UserFetcher.$
                        .username().email().description().gender().home()
                        .status().createTime().modifyTime()
                        .roleList().followersSum().adoresSum()
                        .videos(VideoFetcher.$.allScalarFields())))
                .fetchPage(pageable.getPageNumber(), pageable.getPageSize())
                .map(this::convertToAdminUserDto);
    }

    /**
     * 获取所有视频信息（分页）
     */
    public Page<AdminVideoDto> getAllVideos(Pageable pageable, String status, String keyword) {
        VideoTable videoTable = VideoTable.$;

        return videoRepository.sql()
                .createQuery(videoTable)
                .whereIf(status != null && !status.isEmpty(), videoTable.status().eq(status))
                .whereIf(keyword != null && !keyword.isEmpty(),
                    videoTable.description().like("%" + keyword + "%")
                    .or(videoTable.user().username().like("%" + keyword + "%")))
                .orderBy(videoTable.createTime().desc())
                .select(videoTable.fetch(VideoFetcher.$
                        .description().status().createTime().modifyTime()
                        .likesSum().commentsSum().collectionsSum()
                        .source().poster()
                        .user(UserFetcher.$.username())
                        .type(TypeFetcher.$.name())))
                .fetchPage(pageable.getPageNumber(), pageable.getPageSize())
                .map(this::convertToAdminVideoDto);
    }

    /**
     * 禁用用户
     */
    public void disableUser(Long userId) {
        updateUserStatus(userId, UserStatus.DISABLED);
    }

    /**
     * 启用用户
     */
    public void enableUser(Long userId) {
        updateUserStatus(userId, UserStatus.ACTIVE);
    }

    /**
     * 删除用户（软删除）
     */
    public void deleteUser(Long userId) {
        updateUserStatus(userId, UserStatus.DELETED);
    }

    /**
     * 下架视频
     */
    public void hideVideo(Long videoId) {
        updateVideoStatus(videoId, VideoStatus.HIDDEN);
    }

    /**
     * 上架视频
     */
    public void showVideo(Long videoId) {
        updateVideoStatus(videoId, VideoStatus.ACTIVE);
    }

    /**
     * 删除视频（软删除）
     */
    public void deleteVideo(Long videoId) {
        updateVideoStatus(videoId, VideoStatus.DELETED);
    }

    /**
     * 更新用户状态
     */
    private void updateUserStatus(Long userId, UserStatus status) {
        UserTable userTable = UserTable.$;

        int affected = userRepository.sql()
                .createUpdate(userTable)
                .set(userTable.status(), status.getCode())
                .set(userTable.modifyTime(), new Date())
                .where(userTable.id().eq(userId))
                .execute();

        if (affected == 0) {
            throw new GracefulResponseException("用户不存在或状态更新失败");
        }
    }

    /**
     * 更新视频状态
     */
    private void updateVideoStatus(Long videoId, VideoStatus status) {
        VideoTable videoTable = VideoTable.$;

        int affected = videoRepository.sql()
                .createUpdate(videoTable)
                .set(videoTable.status(), status.getCode())
                .set(videoTable.modifyTime(), new Date())
                .where(videoTable.id().eq(videoId))
                .execute();

        if (affected == 0) {
            throw new GracefulResponseException("视频不存在或状态更新失败");
        }
    }

    /**
     * 转换User实体为AdminUserDto
     */
    private AdminUserDto convertToAdminUserDto(User user) {
        return new AdminUserDto(
                user.id(),
                user.username(),
                user.email(),
                user.description(),
                user.gender(),
                user.home(),
                user.status(),
                user.createTime(),
                user.modifyTime(),
                user.roleList(),
                user.followersSum(),
                user.adoresSum(),
                user.videos().size()
        );
    }

    /**
     * 转换Video实体为AdminVideoDto
     */
    private AdminVideoDto convertToAdminVideoDto(Video video) {
        return new AdminVideoDto(
                video.id(),
                video.description(),
                video.status(),
                video.createTime(),
                video.modifyTime(),
                video.user().username(),
                video.user().id(),
                video.type().name(),
                video.type().id(),
                video.likesSum(),
                video.commentsSum(),
                video.collectionsSum(),
                video.source(),
                video.poster()
        );
    }

    /**
     * 获取系统统计信息
     */
    public AdminStatsDto getSystemStats() {
        UserTable userTable = UserTable.$;
        VideoTable videoTable = VideoTable.$;

        // 统计用户数量
        long totalUsers = userRepository.sql().createQuery(userTable).count();
        long activeUsers = userRepository.sql().createQuery(userTable)
                .where(userTable.status().eq(UserStatus.ACTIVE.getCode())).count();
        long disabledUsers = userRepository.sql().createQuery(userTable)
                .where(userTable.status().eq(UserStatus.DISABLED.getCode())).count();

        // 统计视频数量
        long totalVideos = videoRepository.sql().createQuery(videoTable).count();
        long activeVideos = videoRepository.sql().createQuery(videoTable)
                .where(videoTable.status().eq(VideoStatus.ACTIVE.getCode())).count();
        long hiddenVideos = videoRepository.sql().createQuery(videoTable)
                .where(videoTable.status().eq(VideoStatus.HIDDEN.getCode())).count();

        return new AdminStatsDto(totalUsers, activeUsers, disabledUsers,
                               totalVideos, activeVideos, hiddenVideos);
    }

    /**
     * 系统统计信息DTO
     */
    public static class AdminStatsDto {
        private long totalUsers;
        private long activeUsers;
        private long disabledUsers;
        private long totalVideos;
        private long activeVideos;
        private long hiddenVideos;

        public AdminStatsDto(long totalUsers, long activeUsers, long disabledUsers,
                           long totalVideos, long activeVideos, long hiddenVideos) {
            this.totalUsers = totalUsers;
            this.activeUsers = activeUsers;
            this.disabledUsers = disabledUsers;
            this.totalVideos = totalVideos;
            this.activeVideos = activeVideos;
            this.hiddenVideos = hiddenVideos;
        }

        // Getters
        public long getTotalUsers() { return totalUsers; }
        public long getActiveUsers() { return activeUsers; }
        public long getDisabledUsers() { return disabledUsers; }
        public long getTotalVideos() { return totalVideos; }
        public long getActiveVideos() { return activeVideos; }
        public long getHiddenVideos() { return hiddenVideos; }
    }
}
