package com.celeste.enums;

/**
 * 用户状态枚举
 */
public enum UserStatus {
    /**
     * 正常状态
     */
    ACTIVE("ACTIVE", "正常"),
    
    /**
     * 禁用状态
     */
    DISABLED("DISABLED", "禁用"),
    
    /**
     * 删除状态
     */
    DELETED("DELETED", "删除");

    private final String code;
    private final String description;

    UserStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static UserStatus fromCode(String code) {
        for (UserStatus status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown user status code: " + code);
    }
}
