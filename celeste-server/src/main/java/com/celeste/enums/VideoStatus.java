package com.celeste.enums;

/**
 * 视频状态枚举
 */
public enum VideoStatus {
    /**
     * 正常状态
     */
    ACTIVE("ACTIVE", "正常"),
    
    /**
     * 下架状态
     */
    HIDDEN("HIDDEN", "下架"),
    
    /**
     * 删除状态
     */
    DELETED("DELETED", "删除");

    private final String code;
    private final String description;

    VideoStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static VideoStatus fromCode(String code) {
        for (VideoStatus status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown video status code: " + code);
    }
}
