package com.celeste.entity;

import com.celeste.model.SecurityUser;
import org.babyfish.jimmer.Formula;
import org.babyfish.jimmer.sql.*;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.List;

@Entity
public interface Video extends Base {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    long id();

    String description();

    // 视频状态：ACTIVE(正常), HIDDEN(下架), DELETED(删除)
    @Column(name = "status")
    String status();

    @ManyToOne
    Type type();

    @ManyToOne
    User user();

    @OneToMany(mappedBy = "video")
    List<VideoLike> likes();

    @OneToMany(mappedBy = "video")
    List<Comment> comments();

    @ManyToMany
    List<Collection> collections();

    @Formula(dependencies = "id")
    default String source() {
        return "http://**********:9000/celeste/video/" + id() + ".mp4";
    }

    @Formula(dependencies = "id")
    default String poster() {
        return "http://**********:9000/celeste/poster/" + id() + ".jpg";
    }

    @Formula(dependencies = "likes")
    default long likesSum() {
        return likes().size();
    }

    @Formula(dependencies = "comments")
    default long commentsSum() {
        return comments().size();
    }

    @Formula(dependencies = "collections.user.id")
    default long collectionsSum() {
        return collections().stream().map(collection -> collection.user().id()).distinct().count();
    }

    @Formula(dependencies = "user.followers.id")
    default boolean userIsAdore() {
        Object object = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if ("anonymousUser" == object) return false;
        else {
            SecurityUser securityUser = (SecurityUser) object;
            return !user().followers().stream().filter(follower -> follower.id() == securityUser.user().id()).toList().isEmpty();
        }
    }

    @Formula(dependencies = "likes.user.id")
    default boolean userIsLike() {
        Object object = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if ("anonymousUser" == object) return false;
        else {
            SecurityUser securityUser = (SecurityUser) object;
            return !likes().stream().filter(videoLike -> videoLike.user().id() == securityUser.user().id()).toList().isEmpty();
        }
    }

    @Formula(dependencies = "collections.user.id")
    default boolean userIsCollect() {
        Object object = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if ("anonymousUser" == object) return false;
        else {
            SecurityUser securityUser = (SecurityUser) object;
            return !collections().stream().filter(collection -> collection.user().id() == securityUser.user().id()).toList().isEmpty();
        }
    }

}
