package com.celeste.entity;

import com.celeste.model.SecurityUser;
import org.babyfish.jimmer.Formula;
import org.babyfish.jimmer.sql.*;
import org.jetbrains.annotations.Nullable;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.List;
import java.util.stream.Collectors;

@Entity
public interface User extends Base {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    long id();

    String username();

    String password();

    String email();

    @Nullable
    String description();

    String gender();

    String home();

    // 用户状态：ACTIVE(正常), DISABLED(禁用), DELETED(删除)
    @Column(name = "status")
    String status();

    @ManyToMany
    List<Type> types();

    @ManyToMany
    @JoinTable(name = "adore_follower_mapping", joinColumnName = "follower_id", inverseJoinColumnName = "adore_id")
    List<User> adores();

    @ManyToMany(mappedBy = "adores")
    List<User> followers();

    @ManyToMany
    List<Role> roles();

    @OneToMany(mappedBy = "user")
    List<Video> videos();

    @OneToMany(mappedBy = "user")
    List<Collection> collections();

    @IdView("types")
    List<Long> typeIds();

    @Formula(dependencies = "collections.videoIds")
    default List<Long> collectedVideos() {
        return collections().stream().flatMap(collection -> collection.videoIds().stream()).toList();
    }

    @Formula(dependencies = "adores")
    default long adoresSum() {
        return adores().size();
    }

    @Formula(dependencies = "followers")
    default long followersSum() {
        return followers().size();
    }

    @Formula(dependencies = "roles.name")
    default List<String> roleList() {
        return roles().stream().map(Role::name).collect(Collectors.toList());
    }

    @Formula(dependencies = "roles.authorities.name")
    default List<String> authorityList() {
        return roles().stream().flatMap(role -> role.authorities().stream().map(Authority::name)).collect(Collectors.toList());
    }

    @Formula(dependencies = "followers.id")
    default boolean userIsAdore() {
        Object object = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if ("anonymousUser" == object) return false;
        else {
            SecurityUser securityUser = (SecurityUser) object;
            return !followers().stream().filter(follower -> follower.id() == securityUser.user().id()).toList().isEmpty();
        }
    }

}
