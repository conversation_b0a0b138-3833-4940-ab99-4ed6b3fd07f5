package com.celeste.repository;

import com.celeste.entity.*;
import com.celeste.enums.VideoStatus;
import org.babyfish.jimmer.Page;
import org.babyfish.jimmer.spring.repository.JRepository;
import org.babyfish.jimmer.sql.ast.Expression;
import org.springframework.data.domain.Pageable;

import java.util.Date;
import java.util.List;

public interface VideoRepository extends JRepository<Video, Long> {

    VideoTable table = VideoTable.$;
    VideoLikeTable likeTable = VideoLikeTable.$;

    default List<Video> listVideosByUserId(long userId) {
        return sql().createQuery(table)
                .where(table.userId().eq(userId))
                .where(table.status().eq(VideoStatus.ACTIVE.getCode())) // 只显示正常状态的视频
                .orderBy(table.createTime().desc())
                .select(table.fetch(VideoFetcher.$.description().likesSum().betweenTime().poster().createTime()))
                .execute();
    }

    default Page<Video> listVideosByVideoId(long videoId, Pageable pageable) {
        return sql().createQuery(table)
                .where(table.userId().in(sql().createSubQuery(table).where(table.id().eq(videoId)).select(table.userId())))
                .orderBy(table.createTime().desc())
                .select(table.fetch(VideoFetcher.$.likesSum().poster()))
                .fetchPage(pageable.getPageNumber(), pageable.getPageSize());
    }

    default int insertVideoLike(long userId, long videoId) {
        return sql().insert(Objects.createVideoLike(draft -> draft.setUserId(userId).setVideoId(videoId))).getTotalAffectedRowCount();
    }

    default int deleteVideoLike(long userId, long videoId) {
        return sql().createDelete(likeTable).where(likeTable.userId().eq(userId), likeTable.videoId().eq(videoId)).execute();
    }

    default int collectVideo(long videoId, List<Long> collectionIds) {
        return sql().getAssociations(VideoProps.COLLECTIONS).saveAll(List.of(videoId), collectionIds);
    }

    default int unCollectVideo(long userId, long videoId) {
        List<Long> collectionIds = sql().createQuery(CollectionTable.$)
                .where(CollectionTable.$.userId().eq(userId), CollectionTable.$.videos(video -> video.id().eq(videoId)))
                .select(CollectionTable.$.id())
                .execute();
        return sql().getAssociations(VideoProps.COLLECTIONS).deleteAll(List.of(videoId), collectionIds);
    }

    default Video insertVideo(long userId, long typeId, String description) {
        return sql().insert(Objects.createVideo(draft -> draft
                        .setUserId(userId)
                        .setTypeId(typeId)
                        .setDescription(description)
                        .setStatus(VideoStatus.ACTIVE.getCode()) // 新视频默认为正常状态
                        .setCreateTime(new Date())
                ))
                .getModifiedEntity();
    }

    default List<Video> searchVideo(String keyword) {
        return sql().createQuery(table)
                .where(table.description().ilike(keyword))
                .where(table.status().eq(VideoStatus.ACTIVE.getCode())) // 只搜索正常状态的视频
                .orderBy(sql().createSubQuery(likeTable).where(likeTable.videoId().eq(table.id())).select(Expression.rowCount()).desc())
                .select(table.fetch(VideoFetcher.$.description().likesSum().source().poster().commentsSum().collectionsSum().betweenTime()
                        .user(UserFetcher.$.username())
                ))
                .execute();
    }

}
