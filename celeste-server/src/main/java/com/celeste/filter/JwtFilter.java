package com.celeste.filter;

import cn.hutool.core.convert.Convert;
import cn.hutool.jwt.JWT;
import com.celeste.entity.UserFetcher;
import com.celeste.model.SecurityUser;
import com.celeste.repository.UserRepository;
import com.celeste.utils.JwtUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

@Component
public class JwtFilter extends OncePerRequestFilter {

    @Resource
    private UserRepository userRepository;

    @Override
    public void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        try {
            String authHeader = request.getHeader("Authorization");

            // 如果没有Authorization头或者是空的，直接跳过JWT验证
            if (authHeader == null || authHeader.trim().isEmpty()) {
                filterChain.doFilter(request, response);
                return;
            }

            JWT jwt = JwtUtil.parse(authHeader);
            if (jwt != null) {
                long id = Convert.toLong(jwt.getPayload("id"));
                userRepository.findById(id, UserFetcher.$.username().password().typeIds().roleList())
                        .ifPresent(user -> {
                            SecurityUser securityUser = new SecurityUser(user);
                            UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(securityUser, null, securityUser.getAuthorities());
                            SecurityContextHolder.getContext().setAuthentication(authentication);
                        });
            }
        } catch (Exception e) {
            // JWT解析失败时，不设置认证信息，但继续处理请求
            // 对于不需要认证的接口（如askCode），这样可以正常访问
            System.out.println("JWT解析失败，但继续处理请求: " + e.getMessage());
        }

        filterChain.doFilter(request, response);
    }
}
