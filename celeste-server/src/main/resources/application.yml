spring :
    servlet :
        multipart :
            max-file-size : 200MB
            max-request-size : 200MB
    main :
        banner-mode : off
    datasource :
        username : root
        password : root
        url : ***********************************
    mail :
        host : smtp.qq.com
        username : <EMAIL>
        password : xvltnivwtrlnddbh
        properties :
            mail :
                smtp :
                    auth : true
                    starttls :
                        enable : true
                        required : true
    data :
        redis :
            password : root

jimmer :
    show-sql : true
    pretty-sql : true
    database-validation :
        mode : ERROR
        catalog : celeste


graceful-response :
    response-style : 1
    print-exception-in-global-advice : true
    origin-exception-using-detail-message : true

socketio :
    host : localhost
    port : 9092
    bossCount : 1
    workCount : 100
    allowCustomRequests : true
    upgradeTimeout : 1000000
    pingTimeout : 6000000
    pingInterval : 25000000

