-- 为用户表添加状态字段
ALTER TABLE user ADD COLUMN status VARCHAR(20) DEFAULT 'ACTIVE' NOT NULL COMMENT '用户状态：ACTIVE(正常), DISABLED(禁用), DELETED(删除)';

-- 为视频表添加状态字段  
ALTER TABLE video ADD COLUMN status VARCHAR(20) DEFAULT 'ACTIVE' NOT NULL COMMENT '视频状态：ACTIVE(正常), HIDDEN(下架), DELETED(删除)';

-- 为现有用户设置默认状态
UPDATE user SET status = 'ACTIVE' WHERE status IS NULL OR status = '';

-- 为现有视频设置默认状态
UPDATE video SET status = 'ACTIVE' WHERE status IS NULL OR status = '';

-- 创建索引以提高查询性能
CREATE INDEX idx_user_status ON user(status);
CREATE INDEX idx_video_status ON video(status);
CREATE INDEX idx_user_status_create_time ON user(status, create_time);
CREATE INDEX idx_video_status_create_time ON video(status, create_time);

-- 创建管理员角色（如果不存在）
INSERT IGNORE INTO role (name) VALUES ('ADMIN');

-- 创建管理员权限（如果不存在）
INSERT IGNORE INTO authority (name) VALUES 
('ADMIN_USER_VIEW'),
('ADMIN_USER_MANAGE'), 
('ADMIN_VIDEO_VIEW'),
('ADMIN_VIDEO_MANAGE'),
('ADMIN_SYSTEM_STATS');

-- 为管理员角色分配权限
INSERT IGNORE INTO role_authority_mapping (role_id, authority_id)
SELECT r.id, a.id 
FROM role r, authority a 
WHERE r.name = 'ADMIN' 
AND a.name IN ('ADMIN_USER_VIEW', 'ADMIN_USER_MANAGE', 'ADMIN_VIDEO_VIEW', 'ADMIN_VIDEO_MANAGE', 'ADMIN_SYSTEM_STATS');
