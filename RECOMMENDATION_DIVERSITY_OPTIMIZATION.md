# 推荐系统多样性优化

## 问题描述

原始推荐系统每次返回的视频序列都是固定的，缺乏多样性和随机性，导致用户体验单调。

## 优化方案

### 1. 随机化策略

#### 1.1 动态比例调整
```java
// 动态调整推荐比例，增加随机性
double randomFactor = Math.random() * 0.2 + 0.9; // 0.9-1.1的随机因子

// 关注推荐: 25-35% (原30%)
int followingCount = (int) (count * (0.3 * randomFactor));

// 兴趣推荐: 30-50% (原40%)
int interestCount = (int) (count * (0.4 * randomFactor));

// 热门推荐: 15-25% (原20%)
int hotCount = (int) (count * (0.2 * randomFactor));

// 新视频推荐: 5-15% (原10%)
int newCount = (int) (count * (0.1 * randomFactor));
```

#### 1.2 候选池扩大策略
- **热门视频**: 获取3倍数量的热门视频，随机选择需要的数量
- **兴趣推荐**: 获取2倍数量的相关视频，随机选择
- **新视频**: 获取2倍数量的新视频，随机选择

### 2. 随机化方法

#### 2.1 随机化热门视频推荐
```java
public Set<Long> getRandomizedHotVideos(int count) {
    // 获取更多的热门视频，然后随机选择
    int fetchCount = Math.min(count * 3, 50);
    Set<String> hotVideoIds = RedisUtil.zRevRange("hot_videos", 0, fetchCount - 1);

    List<Long> videoList = hotVideoIds.stream()
            .map(Convert::toLong)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

    // 随机打乱并取前count个
    Collections.shuffle(videoList);
    return videoList.stream()
            .limit(count)
            .collect(Collectors.toSet());
}
```

#### 2.2 随机化兴趣推荐
```java
public Set<Long> getRandomizedInterestRecommendations(long userId, int count) {
    // 基于偏好类型推荐（获取更多然后随机选择）
    if (!preferredTypes.isEmpty()) {
        int fetchCount = Math.min(count * 2, 30);
        List<Long> typeBasedVideos = videoRepository.sql().createQuery(videoTable)
                .where(videoTable.typeId().in(preferredTypes))
                .orderBy(videoTable.createTime().desc())
                .select(videoTable.id())
                .limit(fetchCount)
                .execute();

        // 随机选择一部分
        Collections.shuffle(typeBasedVideos);
        recommendations.addAll(typeBasedVideos.stream()
                .limit(count / 2)
                .collect(Collectors.toSet()));
    }
}
```

#### 2.3 完全随机推荐
```java
public Set<Long> getRandomVideos(int count) {
    try {
        // 使用原生SQL实现数据库级随机查询
        List<Long> randomVideoIds = videoRepository.sql()
                .createNativeQuery("SELECT id FROM video ORDER BY RAND() LIMIT ?")
                .setParameter(1, count * 2)
                .execute()
                .stream()
                .map(row -> ((Number) row[0]).longValue())
                .collect(Collectors.toList());

        Collections.shuffle(randomVideoIds);
        return randomVideoIds.stream()
                .limit(count)
                .collect(Collectors.toSet());
    } catch (Exception e) {
        // 备用方案：Java内存随机
        return getRandomVideosFallback(count);
    }
}

// 备用方案：获取所有ID然后在Java中随机选择
private Set<Long> getRandomVideosFallback(int count) {
    List<Long> allVideoIds = videoRepository.sql().createQuery(videoTable)
            .select(videoTable.id())
            .execute();
    Collections.shuffle(allVideoIds);
    return allVideoIds.stream().limit(count).collect(Collectors.toSet());
}
```

### 3. 最终结果随机化

```java
// 打乱推荐结果，增加多样性
Collections.shuffle(videos);
return videos;
```

## 优化效果

### 1. 多样性提升
- **动态比例**: 每次推荐的各类型视频比例略有不同
- **候选池扩大**: 从更大的候选集中随机选择
- **多层随机**: 在获取、选择、排序多个环节都加入随机性

### 2. 用户体验改善
- **避免重复**: 连续多次推荐不会完全相同
- **保持相关性**: 在随机性的同时保持推荐的相关性
- **探索发现**: 用户有机会发现更多不同类型的内容

### 3. 算法平衡
- **智能与随机**: 70%基于算法，30%基于随机
- **个性化保持**: 核心推荐逻辑不变，只是增加变化
- **性能优化**: 随机化不影响查询性能

## 配置参数

### 1. 随机因子范围
```java
double randomFactor = Math.random() * 0.2 + 0.9; // 可调整为0.8-1.2等
```

### 2. 候选池倍数
- 热门视频: 3倍 (可调整为2-5倍)
- 兴趣推荐: 2倍 (可调整为1.5-3倍)
- 新视频: 2倍 (可调整为1.5-3倍)

### 3. 最大候选数量
- 热门视频: 最多50个
- 兴趣推荐: 最多30个
- 新视频: 最多30个

## 监控指标

### 1. 多样性指标
- 推荐结果重复率
- 不同类型视频分布
- 用户点击分散度

### 2. 用户行为指标
- 点击率变化
- 观看时长变化
- 用户满意度

### 3. 系统性能指标
- 推荐响应时间
- 数据库查询次数
- 缓存命中率

## 未来优化方向

1. **机器学习**: 使用ML模型预测用户偏好变化
2. **实时调整**: 根据用户实时行为调整随机性程度
3. **A/B测试**: 对比不同随机化策略的效果
4. **个性化随机**: 为不同用户设置不同的随机化程度
