import { toast } from '@/components/toast/index.js'
import axios from 'axios'
import Cookies from 'js-cookie'
import { get, merge } from 'lodash-es'

const createService = () => {
    const service = axios.create()
    service.interceptors.response.use(
        ({ data: responseData }) => {
            const { code, data, msg } = responseData
            switch (code) {
                case '0':
                    return data
                case '1':
                    toast.warning(msg)
                    return Promise.reject(new Error(msg))
            }
        },
        (error) => {
            const status = get(error, 'response.status')
            switch (status) {
                case 400:
                    error.message = '请求参数错误'
                    break
                case 401:
                    error.message = '认证已过期，请重新登录'
                    // logout()
                    break
                case 403:
                    error.message = '权限不足'
                    break
                case 500:
                    error.message = '服务器内部错误'
                    break
                default:
                    error.message = '未知错误，请联系管理员'
            }
            toast.error(error.message)
            return Promise.reject(error)
        }
    )
    return service
}

const createRequest = (service) => {
    return function (config) {
        const jwt = Cookies.get('jwt')
        const defaultConfig = {
            timeout: 5000,
            baseURL: 'http://localhost:8080',
            headers: {
                'Content-Type': 'application/json'
            }
        }

        // 只有当JWT存在且有效时才添加Authorization头
        if (jwt && jwt.trim() !== '' && jwt !== 'undefined' && jwt !== 'null') {
            defaultConfig.headers.Authorization = `Bearer ${jwt}`
        }

        const mergeConfig = merge(defaultConfig, config)
        return service(mergeConfig)
    }
}

export const request = createRequest(createService())
